<?php
    $database = "localhost";
    $hostname = "anaqati_db";
    $username = "root";
    $password = "";


    $conn = new mysqli($hostname, $username, $password, $hostname);

    if ($conn->connect_error) {
        die("فشل الاتصال بقاعدة البيانات: " . $conn->connect_error);
    }

// دالة لتنظيف البيانات المدخلة
function sanitize_input($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

// دالة لتحويل التاريخ إلى التنسيق العربي
function format_arabic_date($date) {
    $timestamp = strtotime($date);
    return date('Y/m/d H:i', $timestamp);
}

// دالة لتنسيق السعر
function format_price($price) {
    return number_format($price, 2) . ' ريال';
}

// دالة للحصول على جميع المنتجات
function get_all_products($pdo) {
    try {
        $stmt = $pdo->query("SELECT * FROM products ORDER BY created_at DESC");
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log("خطأ في جلب المنتجات: " . $e->getMessage());
        return [];
    }
}

// دالة للحصول على جميع الأقسام
function get_all_categories($pdo) {
    try {
        $stmt = $pdo->query("SELECT * FROM categories ORDER BY name");
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log("خطأ في جلب الأقسام: " . $e->getMessage());
        return [];
    }
}

// دالة للحصول على الأكثر مبيعاً
function get_bestsellers($pdo) {
    try {
        $stmt = $pdo->query("SELECT * FROM `most-sell` ORDER BY id");
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log("خطأ في جلب الأكثر مبيعاً: " . $e->getMessage());
        return [];
    }
}

// دالة للحصول على منتج واحد
function get_product_by_id($pdo, $id) {
    try {
        $stmt = $pdo->prepare("SELECT * FROM products WHERE id = ?");
        $stmt->execute([$id]);
        return $stmt->fetch();
    } catch (PDOException $e) {
        error_log("خطأ في جلب المنتج: " . $e->getMessage());
        return false;
    }
}

// دالة لحذف منتج
function delete_product($pdo, $id) {
    try {
        $stmt = $pdo->prepare("DELETE FROM products WHERE id = ?");
        return $stmt->execute([$id]);
    } catch (PDOException $e) {
        error_log("خطأ في حذف المنتج: " . $e->getMessage());
        return false;
    }
}

// دالة لإضافة منتج جديد
function add_product($pdo, $name, $description, $image, $price, $old_price, $category) {
    try {
        $stmt = $pdo->prepare("INSERT INTO products (name, description, image, price, old_price, category) VALUES (?, ?, ?, ?, ?, ?)");
        return $stmt->execute([$name, $description, $image, $price, $old_price, $category]);
    } catch (PDOException $e) {
        error_log("خطأ في إضافة المنتج: " . $e->getMessage());
        return false;
    }
}

// دالة لتحديث منتج
function update_product($pdo, $id, $name, $description, $image, $price, $old_price, $category) {
    try {
        $stmt = $pdo->prepare("UPDATE products SET name = ?, description = ?, image = ?, price = ?, old_price = ?, category = ? WHERE id = ?");
        return $stmt->execute([$name, $description, $image, $price, $old_price, $category, $id]);
    } catch (PDOException $e) {
        error_log("خطأ في تحديث المنتج: " . $e->getMessage());
        return false;
    }
}

// دالة للبحث في المنتجات
function search_products($pdo, $search_term, $category = null) {
    try {
        $sql = "SELECT * FROM products WHERE name LIKE ?";
        $params = ['%' . $search_term . '%'];
        
        if ($category) {
            $sql .= " AND category = ?";
            $params[] = $category;
        }
        
        $sql .= " ORDER BY created_at DESC";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log("خطأ في البحث: " . $e->getMessage());
        return [];
    }
}

// دالة للحصول على إحصائيات لوحة التحكم
function get_dashboard_stats($pdo) {
    try {
        $stats = [];
        
        // عدد المنتجات
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM products");
        $stats['products_count'] = $stmt->fetch()['count'];
        
        // عدد الأقسام
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM categories");
        $stats['categories_count'] = $stmt->fetch()['count'];
        
        // عدد المستخدمين
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
        $stats['users_count'] = $stmt->fetch()['count'];
        
        return $stats;
    } catch (PDOException $e) {
        error_log("خطأ في جلب الإحصائيات: " . $e->getMessage());
        return ['products_count' => 0, 'categories_count' => 0, 'users_count' => 0];
    }
}
?>
