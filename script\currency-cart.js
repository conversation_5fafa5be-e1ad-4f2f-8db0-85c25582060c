// تحديث أسعار السلة حسب العملة
function updateCartPrices() {
    let total = 0;
    if (!window.cart || !Array.isArray(window.cart)) return;
    // تحديث الأسعار في عناصر السلة
    const cartItemsList = document.getElementById('cart-items');
    if (cartItemsList) {
        Array.from(cartItemsList.children).forEach((li, idx) => {
            const item = window.cart[idx];
            if (!item) return;
            // استخراج السعر الأصلي بالريال اليمني من خاصية مخصصة أو من اسم المنتج
            let priceYER = item.priceYER || extractYER(item.price);
            // تحديث السعر في العنصر
            const priceSpan = li.querySelector('span[style*="color:#ff2a6d"]');
            if (priceSpan) {
                priceSpan.textContent = formatPrice(priceYER);
            }
            total += convertPrice(priceYER) * item.quantity;
        });
    }
    // تحديث المجموع
    const cartTotal = document.getElementById('cart-total');
    if (cartTotal) {
        cartTotal.textContent = total.toLocaleString();
    }
    // تحديث اسم العملة بجانب المجموع
    const cartCurrency = document.getElementById('cart-currency');
    if (cartCurrency && typeof currentCurrency !== 'undefined') {
        cartCurrency.textContent = currentCurrency === 'YER' ? 'ر.ق' : 'ر.س';
    }
}

// استخراج السعر بالريال اليمني من نص السعر (مثلاً "1,700 ريال")
function extractYER(priceText) {
    if (!priceText) return 0;
    return Number(priceText.replace(/[^\d]/g, ''));
}

// عند إضافة منتج للسلة، احفظ السعر الأصلي بالريال اليمني
(function patchAddToCart() {
    if (!window.addToCart) return;
    const origAddToCart = window.addToCart;
    window.addToCart = function(button) {
        const productCard = button.closest('.product-card');
        const currentPriceEl = productCard.querySelector('.current-price');
        let priceYER = currentPriceEl && currentPriceEl.dataset.yer ? Number(currentPriceEl.dataset.yer) : extractYER(currentPriceEl.textContent);
        // استدعاء الدالة الأصلية مع تعديل السعر
        const product = origAddToCart.call(this, button);
        if (product && typeof product === 'object') {
            product.priceYER = priceYER;
            product.price = formatPrice(priceYER);
        }
        updateAllPrices();
        return product;
    }
})();
