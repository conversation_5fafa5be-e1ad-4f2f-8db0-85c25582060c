// currency.js
// إعدادات العملة
const CURRENCY_RATES = {
    YER: 1, // الريال اليمني هو الأساس
    SAR: 140 // 1 ريال سعودي = 66 ريال يمني
};
const CURRENCY_SYMBOLS = {
    YER: 'ر.ق', // ريال يمني
    SAR: 'ر.س'  // ريال سعودي
};

let currentCurrency = localStorage.getItem('currency') || 'YER';

// عند تحميل الصفحة، اضبط العملة المختارة
const currencySelect = document.getElementById('currency-select');
if (currencySelect) {
    currencySelect.value = currentCurrency;
    currencySelect.addEventListener('change', function() {
        currentCurrency = this.value;
        localStorage.setItem('currency', currentCurrency);
        updateAllPrices();
    });
}

// دالة لتحويل السعر
function convertPrice(priceYER) {
    if (currentCurrency === 'YER') return priceYER;
    return Math.round((priceYER / CURRENCY_RATES['SAR']));
}

// دالة لتنسيق السعر مع العملة
function formatPrice(priceYER) {
    const price = convertPrice(priceYER);
    return price.toLocaleString() + ' ' + CURRENCY_SYMBOLS[currentCurrency];
}

// تحديث جميع الأسعار في الموقع
function updateAllPrices() {
    // المنتجات
    document.querySelectorAll('.product-card').forEach(card => {
        const currentPriceEl = card.querySelector('.current-price');
        const oldPriceEl = card.querySelector('.old-price');
        if (currentPriceEl && currentPriceEl.dataset.yer) {
            currentPriceEl.textContent = formatPrice(Number(currentPriceEl.dataset.yer));
        }
        if (oldPriceEl && oldPriceEl.dataset.yer) {
            oldPriceEl.textContent = formatPrice(Number(oldPriceEl.dataset.yer));
        }
    });
    // الأكثر مبيعاً
    document.querySelectorAll('.bestseller-price').forEach(el => {
        if (el.dataset.yer) {
            el.textContent = formatPrice(Number(el.dataset.yer));
        }
    });
    // أسعار السلة
    if (typeof updateCartPrices === 'function') updateCartPrices();
}

// عند تحميل الصفحة
window.addEventListener('DOMContentLoaded', updateAllPrices);
