
// DOM Elements
const sidebarNavItems = document.querySelectorAll('.nav-item a');
const contentSections = document.querySelectorAll('.content-section');
const pageTitle = document.getElementById('page-title');
const productsTableBody = document.getElementById('products-table-body');
const categoryFilter = document.getElementById('category-filter');
const statusFilter = document.getElementById('status-filter');
const productSearch = document.getElementById('product-search');
const sidebarToggle = document.querySelector('.sidebar-toggle');
const sidebar = document.querySelector('.sidebar');

// Initialize Dashboard
document.addEventListener('DOMContentLoaded', function() {
    loadProductsTable();
    setupEventListeners();
    setupResponsiveSidebar();
});

function setupEventListeners() {
    // Sidebar navigation
    sidebarNavItems.forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            const section = this.getAttribute('data-section');
            switchSection(section);
            
            // Update active nav item
            sidebarNavItems.forEach(nav => nav.parentElement.classList.remove('active'));
            this.parentElement.classList.add('active');
        });
    });
    
    // Product filters
    categoryFilter.addEventListener('change', filterProducts);
    statusFilter.addEventListener('change', filterProducts);
    productSearch.addEventListener('input', filterProducts);
    
    // Add product button
    const addProductBtn = document.getElementById('add-product-btn');
    if (addProductBtn) {
        addProductBtn.addEventListener('click', function() {
            alert('فتح نموذج إضافة منتج جديد');
        });
    }
    
    // Add category button
    const addCategoryBtn = document.getElementById('add-category-btn');
    if (addCategoryBtn) {
        addCategoryBtn.addEventListener('click', function() {
            alert('فتح نموذج إضافة قسم جديد');
        });
    }
    
    // Refresh bestsellers button
    const refreshBestsellersBtn = document.getElementById('refresh-bestsellers');
    if (refreshBestsellersBtn) {
        refreshBestsellersBtn.addEventListener('click', function() {
            alert('تم تحديث قائمة الأكثر مبيعاً');
        });
    }
}

// Setup Responsive Sidebar
function setupResponsiveSidebar() {
    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', function() {
            sidebar.classList.toggle('active');
        });
    }
    
    // Close sidebar when clicking outside on mobile
    document.addEventListener('click', function(e) {
        if (window.innerWidth <= 768) {
            if (!sidebar.contains(e.target) && !sidebarToggle.contains(e.target)) {
                sidebar.classList.remove('active');
            }
        }
    });
}

// Switch Section
function switchSection(sectionName) {
    // Hide all sections
    contentSections.forEach(section => {
        section.classList.remove('active');
    });
    
    // Show selected section
    const targetSection = document.getElementById(sectionName + '-section');
    if (targetSection) {
        targetSection.classList.add('active');
    }
    
    // Update page title
    const titles = {
        'dashboard': 'الرئيسية',
        'products': 'إدارة المنتجات',
        'bestsellers': 'الأكثر مبيعاً',
        'categories': 'إدارة الأقسام',
    };
    
    pageTitle.textContent = titles[sectionName] || 'لوحة التحكم';
}


// Filter Products
function filterProducts() {
    const categoryValue = categoryFilter.value;
    const statusValue = statusFilter.value;
    const searchValue = productSearch.value.toLowerCase();
    
    let filteredProducts = productsData;
    
    // Filter by category
    if (categoryValue) {
        filteredProducts = filteredProducts.filter(product => product.category === categoryValue);
    }
    
    // Filter by status
    if (statusValue) {
        filteredProducts = filteredProducts.filter(product => product.status === statusValue);
    }
    
    // Filter by search
    if (searchValue) {
        filteredProducts = filteredProducts.filter(product => 
            product.name.toLowerCase().includes(searchValue)
        );
    }
    
    loadProductsTable(filteredProducts);
}

// Product Actions
function editProduct(productId) {
    const product = productsData.find(p => p.id === productId);
    if (product) {
        alert(`تعديل المنتج: ${product.name}`);
        // Here you would open an edit modal or navigate to edit page
    }
}

function deleteProduct(productId) {
    const product = productsData.find(p => p.id === productId);
    if (product) {
        if (confirm(`هل أنت متأكد من حذف المنتج: ${product.name}؟`)) {
            // Remove from data array
            const index = productsData.findIndex(p => p.id === productId);
            if (index > -1) {
                productsData.splice(index, 1);
                loadProductsTable();
                alert('تم حذف المنتج بنجاح');
            }
        }
    }
}


// Add the CSS to the document
const style = document.createElement('style');
style.textContent = additionalCSS;
document.head.appendChild(style);

// Simulate real-time updates
setInterval(function() {
    // Update notification count randomly
    const notificationCount = document.querySelector('.notification-count');
    if (notificationCount) {
        const randomCount = Math.floor(Math.random() * 10) + 1;
        notificationCount.textContent = randomCount;
    }
    
    // Update stats randomly (simulate real-time data)
    const statNumbers = document.querySelectorAll('.stat-number');
    statNumbers.forEach(stat => {
        if (stat.textContent.includes('ريال')) {
            const currentValue = parseInt(stat.textContent.replace(/[^\d]/g, ''));
            const change = Math.floor(Math.random() * 1000) - 500;
            const newValue = Math.max(0, currentValue + change);
            stat.textContent = newValue.toLocaleString() + ' ريال';
        }
    });
}, 30000); // Update every 30 seconds
