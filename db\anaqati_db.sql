CREATE DATABASE IF NOT EXISTS anaqati_db DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE anaqati_db;

-- جدول المستخدمين
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(100) NOT NULL UNIQUE,
    email VARCHAR(150) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول المنتجات
CREATE TABLE IF NOT EXISTS products (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    image VARCHAR(255),
    price DECIMAL(10,2) NOT NULL,
    old_price DECIMAL(10,2),
    category VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS most-sell (
    id INT AUTO_INCREMENT PRIMARY KEY,
    description text,
    image varchar(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    image VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- بيانات عشوائية للمستخدمين
INSERT INTO users (username, email, password) VALUES
('admin', '<EMAIL>', '$2y$10$examplehashforadmin'),
('user1', '<EMAIL>', '$2y$10$examplehashforuser1'),
('user2', '<EMAIL>', '$2y$10$examplehashforuser2');

-- بيانات عشوائية للأقسام
INSERT INTO categories (name, description, image) VALUES
('العناية بالبشرة', 'منتجات لتنظيف وترطيب البشرة', 'img/scrub1.webp'),
('العناية بالشعر', 'منتجات طبيعية للشعر', 'img/shampoo.jpg'),
('العناية بالجسم', 'مرطبات ومقشرات للجسم', 'img/pack2.webp'),
('منتجات عضوية', 'منتجات طبيعية 100%', 'img/pack1.webp');

-- بيانات عشوائية للمنتجات
INSERT INTO products (name, description, image, price, old_price, category) VALUES
('كريم مرطب طبيعي للوجه', 'كريم مرطب للبشرة الحساسة', 'img/shampoo.jpg', 1700, 1000, 'العناية بالبشرة'),
('شامبو عضوي للشعر الجاف', 'شامبو طبيعي للشعر الجاف', 'img/shampoo2.jpg', 2500, NULL, 'العناية بالشعر'),
('زيت الأرغان للجسم', 'زيت طبيعي لترطيب الجسم', 'img/pack1.webp', 2000, 1200, 'العناية بالجسم'),
('قناع الطين الطبيعي', 'قناع طين للبشرة الدهنية', 'img/scrub1.webp', 1500, NULL, 'العناية بالبشرة');

-- بيانات عشوائية للأكثر مبيعاً
INSERT INTO most-sell (description, image) VALUES
('كريم أساس فائق التغطية', 'img/shampoo2.jpg'),
('مجموعة العناية الكاملة 1', 'img/pack1.webp'),
('مجموعة العناية الكاملة 2', 'img/pack2.webp');
