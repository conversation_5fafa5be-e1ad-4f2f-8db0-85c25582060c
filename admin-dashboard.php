<?php
session_start();
require_once 'includes/db_connection.php';

// الحصول على البيانات من قاعدة البيانات
$products = get_all_products($conn);
$categories = get_all_categories($conn);
$bestsellers = get_bestsellers($conn);
$stats = get_dashboard_stats($conn);

// معالجة الفلترة والبحث
$filtered_products = $products;
$search_term = $_GET['search'] ?? '';
$category_filter = $_GET['category'] ?? '';

if (!empty($search_term) || !empty($category_filter)) {
    $filtered_products = search_products($conn, $search_term, $category_filter);
}

// الحصول على القسم النشط
$active_section = $_GET['section'] ?? 'dashboard';

// ملء نموذج المنتج عند التعديل من قاعدة البيانات
$edit_product = null;
if ($active_section === 'products' && isset($_GET['edit'])) {
    $edit_id = intval($_GET['edit']);
    $edit_product = get_product_by_id($conn, $edit_id);
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - إدارة المنتجات</title>
    <link rel="stylesheet" href="css/admin-dashboard.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h2><i class="fas fa-crown"></i> أناقتي</h2>
            <p>لوحة التحكم</p>
        </div>
        
        <nav class="sidebar-nav">
            <ul>
                <li class="nav-item <?php echo $active_section === 'dashboard' ? 'active' : ''; ?>">
                    <a href="?section=dashboard">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>الرئيسية</span>
                    </a>
                </li>
                <li class="nav-item <?php echo $active_section === 'products' ? 'active' : ''; ?>">
                    <a href="?section=products">
                        <i class="fas fa-box"></i>
                        <span>إدارة المنتجات</span>
                    </a>
                </li>
                <li class="nav-item <?php echo $active_section === 'bestsellers' ? 'active' : ''; ?>">
                    <a href="?section=bestsellers">
                        <i class="fas fa-fire"></i>
                        <span>الأكثر مبيعاً</span>
                    </a>
                </li>
                <li class="nav-item <?php echo $active_section === 'categories' ? 'active' : ''; ?>">
                    <a href="?section=categories">
                        <i class="fas fa-tags"></i>
                        <span>الأقسام</span>
                    </a>
                </li>
            </ul>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Header -->
        <header class="admin-header">
            <div class="header-left">
                <button class="sidebar-toggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1 id="page-title">
                    <?php
                    $titles = [
                        'dashboard' => 'الرئيسية',
                        'products' => 'إدارة المنتجات',
                        'bestsellers' => 'الأكثر مبيعاً',
                        'categories' => 'إدارة الأقسام'
                    ];
                    echo $titles[$active_section] ?? 'لوحة التحكم';
                    ?>
                </h1>
            </div>
            <div class="header-right">
                <div class="search-box">
                    <input type="text" placeholder="البحث...">
                    <i class="fas fa-search"></i>
                </div>
                <div class="admin-profile">
                    <img src="img/admin-avatar.jpg" alt="المدير" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiNkNGE0YTQiLz4KPHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1zbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDEyQzE0LjIwOTEgMTIgMTYgMTAuMjA5MSAxNiA4QzE2IDUuNzkwODYgMTQuMjA5MSA0IDEyIDRDOS43OTA4NiA0IDggNS43OTA4NiA4IDhDOCAxMC4yMDkxIDkuNzkwODYgMTIgMTIgMTJaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNMTIgMTRDOC42ODYyOSAxNCA2IDE2LjY4NjMgNiAyMFYyMkgxOFYyMEMxOCAxNi42ODYzIDE1LjMxMzcgMTQgMTIgMTRaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4KPC9zdmc+'">
                    <span>المدير</span>
                </div>
            </div>
        </header>

        <!-- رسائل النجاح والخطأ -->
        <?php if (isset($_SESSION['admin_message'])): ?>
            <div class="alert alert-<?php echo $_SESSION['admin_message_type']; ?>">
                <i class="fas fa-<?php echo $_SESSION['admin_message_type'] === 'success' ? 'check-circle' : 'exclamation-circle'; ?>"></i>
                <?php echo $_SESSION['admin_message']; ?>
                <button class="alert-close" onclick="this.parentElement.style.display='none'">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <?php
            unset($_SESSION['admin_message']);
            unset($_SESSION['admin_message_type']);
            ?>
        <?php endif; ?>

        <!-- Dashboard Content -->
        <div class="content-area">
            <!-- Dashboard Section -->
            <section id="dashboard-section" class="content-section <?php echo $active_section === 'dashboard' ? 'active' : ''; ?>">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-box"></i>
                        </div>
                        <div class="stat-info">
                            <h3>إجمالي المنتجات</h3>
                            <p class="stat-number"><?php echo $stats['products_count']; ?></p>
                            <span class="stat-change positive">منتج متاح</span>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-tags"></i>
                        </div>
                        <div class="stat-info">
                            <h3>عدد الأقسام</h3>
                            <p class="stat-number"><?php echo $stats['categories_count']; ?></p>
                            <span class="stat-change">قسم نشط</span>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-info">
                            <h3>عدد المستخدمين</h3>
                            <p class="stat-number"><?php echo $stats['users_count']; ?></p>
                            <span class="stat-change">مستخدم مسجل</span>
   