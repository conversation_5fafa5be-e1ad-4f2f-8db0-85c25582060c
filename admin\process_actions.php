<?php
session_start();
require_once '../includes/db_connection.php';

// التحقق من وجود طلب POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Location: ../admin-dashboard.php');
    exit;
}

$action = $_POST['action'] ?? '';
$response = ['success' => false, 'message' => ''];

switch ($action) {
    case 'add_product':
        $response = handle_add_product($pdo);
        break;
        
    case 'edit_product':
        $response = handle_edit_product($pdo);
        break;
        
    case 'delete_product':
        $response = handle_delete_product($pdo);
        break;
        
    case 'add_category':
        $response = handle_add_category($pdo);
        break;
        
    case 'edit_category':
        $response = handle_edit_category($pdo);
        break;
        
    case 'delete_category':
        $response = handle_delete_category($pdo);
        break;
        
    default:
        $response['message'] = 'عملية غير صحيحة';
}

// إعادة التوجيه مع رسالة
$_SESSION['admin_message'] = $response['message'];
$_SESSION['admin_message_type'] = $response['success'] ? 'success' : 'error';
header('Location: ../admin-dashboard.php');
exit;

// دالة إضافة منتج
function handle_add_product($pdo) {
    $name = sanitize_input($_POST['product_name'] ?? '');
    $description = sanitize_input($_POST['product_description'] ?? '');
    $price = floatval($_POST['product_price'] ?? 0);
    $old_price = !empty($_POST['product_old_price']) ? floatval($_POST['product_old_price']) : null;
    $category = sanitize_input($_POST['product_category'] ?? '');
    
    // معالجة رفع الصورة
    $image = '';
    if (isset($_FILES['product_image']) && $_FILES['product_image']['error'] === UPLOAD_ERR_OK) {
        $image = handle_image_upload($_FILES['product_image']);
        if (!$image) {
            return ['success' => false, 'message' => 'خطأ في رفع الصورة'];
        }
    }
    
    if (empty($name) || empty($price) || empty($category)) {
        return ['success' => false, 'message' => 'يرجى ملء جميع الحقول المطلوبة'];
    }
    
    if (add_product($pdo, $name, $description, $image, $price, $old_price, $category)) {
        return ['success' => true, 'message' => 'تم إضافة المنتج بنجاح'];
    } else {
        return ['success' => false, 'message' => 'خطأ في إضافة المنتج'];
    }
}

// دالة تعديل منتج
function handle_edit_product($pdo) {
    $id = intval($_POST['product_id'] ?? 0);
    $name = sanitize_input($_POST['product_name'] ?? '');
    $description = sanitize_input($_POST['product_description'] ?? '');
    $price = floatval($_POST['product_price'] ?? 0);
    $old_price = !empty($_POST['product_old_price']) ? floatval($_POST['product_old_price']) : null;
    $category = sanitize_input($_POST['product_category'] ?? '');
    
    if ($id <= 0) {
        return ['success' => false, 'message' => 'معرف المنتج غير صحيح'];
    }
    
    // الحصول على المنتج الحالي
    $current_product = get_product_by_id($pdo, $id);
    if (!$current_product) {
        return ['success' => false, 'message' => 'المنتج غير موجود'];
    }
    
    $image = $current_product['image']; // الاحتفاظ بالصورة الحالية
    
    // معالجة رفع صورة جديدة
    if (isset($_FILES['product_image']) && $_FILES['product_image']['error'] === UPLOAD_ERR_OK) {
        $new_image = handle_image_upload($_FILES['product_image']);
        if ($new_image) {
            // حذف الصورة القديمة
            if ($image && file_exists('../' . $image)) {
                unlink('../' . $image);
            }
            $image = $new_image;
        }
    }
    
    if (empty($name) || empty($price) || empty($category)) {
        return ['success' => false, 'message' => 'يرجى ملء جميع الحقول المطلوبة'];
    }
    
    if (update_product($pdo, $id, $name, $description, $image, $price, $old_price, $category)) {
        return ['success' => true, 'message' => 'تم تحديث المنتج بنجاح'];
    } else {
        return ['success' => false, 'message' => 'خطأ في تحديث المنتج'];
    }
}

// دالة حذف منتج
function handle_delete_product($pdo) {
    $id = intval($_POST['product_id'] ?? 0);
    
    if ($id <= 0) {
        return ['success' => false, 'message' => 'معرف المنتج غير صحيح'];
    }
    
    // الحصول على المنتج لحذف الصورة
    $product = get_product_by_id($pdo, $id);
    if ($product && $product['image'] && file_exists('../' . $product['image'])) {
        unlink('../' . $product['image']);
    }
    
    if (delete_product($pdo, $id)) {
        return ['success' => true, 'message' => 'تم حذف المنتج بنجاح'];
    } else {
        return ['success' => false, 'message' => 'خطأ في حذف المنتج'];
    }
}

// دالة إضافة قسم
function handle_add_category($pdo) {
    $name = sanitize_input($_POST['category_name'] ?? '');
    $description = sanitize_input($_POST['category_description'] ?? '');
    
    // معالجة رفع الصورة
    $image = '';
    if (isset($_FILES['category_image']) && $_FILES['category_image']['error'] === UPLOAD_ERR_OK) {
        $image = handle_image_upload($_FILES['category_image']);
        if (!$image) {
            return ['success' => false, 'message' => 'خطأ في رفع الصورة'];
        }
    }
    
    if (empty($name)) {
        return ['success' => false, 'message' => 'يرجى إدخال اسم القسم'];
    }
    
    try {
        $stmt = $pdo->prepare("INSERT INTO categories (name, description, image) VALUES (?, ?, ?)");
        if ($stmt->execute([$name, $description, $image])) {
            return ['success' => true, 'message' => 'تم إضافة القسم بنجاح'];
        } else {
            return ['success' => false, 'message' => 'خطأ في إضافة القسم'];
        }
    } catch (PDOException $e) {
        return ['success' => false, 'message' => 'خطأ في قاعدة البيانات'];
    }
}

// دالة تعديل قسم
function handle_edit_category($pdo) {
    $id = intval($_POST['category_id'] ?? 0);
    $name = sanitize_input($_POST['category_name'] ?? '');
    $description = sanitize_input($_POST['category_description'] ?? '');
    
    if ($id <= 0 || empty($name)) {
        return ['success' => false, 'message' => 'بيانات غير صحيحة'];
    }
    
    // الحصول على القسم الحالي
    try {
        $stmt = $pdo->prepare("SELECT * FROM categories WHERE id = ?");
        $stmt->execute([$id]);
        $current_category = $stmt->fetch();
        
        if (!$current_category) {
            return ['success' => false, 'message' => 'القسم غير موجود'];
        }
        
        $image = $current_category['image']; // الاحتفاظ بالصورة الحالية
        
        // معالجة رفع صورة جديدة
        if (isset($_FILES['category_image']) && $_FILES['category_image']['error'] === UPLOAD_ERR_OK) {
            $new_image = handle_image_upload($_FILES['category_image']);
            if ($new_image) {
                // حذف الصورة القديمة
                if ($image && file_exists('../' . $image)) {
                    unlink('../' . $image);
                }
                $image = $new_image;
            }
        }
        
        $stmt = $pdo->prepare("UPDATE categories SET name = ?, description = ?, image = ? WHERE id = ?");
        if ($stmt->execute([$name, $description, $image, $id])) {
            return ['success' => true, 'message' => 'تم تحديث القسم بنجاح'];
        } else {
            return ['success' => false, 'message' => 'خطأ في تحديث القسم'];
        }
    } catch (PDOException $e) {
        return ['success' => false, 'message' => 'خطأ في قاعدة البيانات'];
    }
}

// دالة حذف قسم
function handle_delete_category($pdo) {
    $id = intval($_POST['category_id'] ?? 0);
    
    if ($id <= 0) {
        return ['success' => false, 'message' => 'معرف القسم غير صحيح'];
    }
    
    try {
        // الحصول على القسم لحذف الصورة
        $stmt = $pdo->prepare("SELECT * FROM categories WHERE id = ?");
        $stmt->execute([$id]);
        $category = $stmt->fetch();
        
        if ($category && $category['image'] && file_exists('../' . $category['image'])) {
            unlink('../' . $category['image']);
        }
        
        $stmt = $pdo->prepare("DELETE FROM categories WHERE id = ?");
        if ($stmt->execute([$id])) {
            return ['success' => true, 'message' => 'تم حذف القسم بنجاح'];
        } else {
            return ['success' => false, 'message' => 'خطأ في حذف القسم'];
        }
    } catch (PDOException $e) {
        return ['success' => false, 'message' => 'خطأ في قاعدة البيانات'];
    }
}

// دالة معالجة رفع الصور
function handle_image_upload($file) {
    $upload_dir = '../img/';
    $allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    $max_size = 5 * 1024 * 1024; // 5MB
    
    // التحقق من نوع الملف
    if (!in_array($file['type'], $allowed_types)) {
        return false;
    }
    
    // التحقق من حجم الملف
    if ($file['size'] > $max_size) {
        return false;
    }
    
    // إنشاء اسم فريد للملف
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = uniqid() . '.' . $extension;
    $filepath = $upload_dir . $filename;
    
    // رفع الملف
    if (move_uploaded_file($file['tmp_name'], $filepath)) {
        return 'img/' . $filename;
    }
    
    return false;
}
?>
