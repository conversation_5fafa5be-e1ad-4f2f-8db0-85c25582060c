<?php
    $database = "anaqati_db";
    $server = "localhost";
    $username = "root";
    $password = "";

    $conn = new mysqli($server, $username, $password, $database);

    if ($conn->connect_error) {
        die("فشل الاتصال بقاعدة البيانات: " . $conn->connect_error);
    }

// دالة لتنظيف البيانات المدخلة
function sanitize_input($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

// دالة لتحويل التاريخ إلى التنسيق العربي
function format_arabic_date($date) {
    $timestamp = strtotime($date);
    return date('Y/m/d H:i', $timestamp);
}

// دالة لتنسيق السعر
function format_price($price) {
    return number_format($price, 2) . ' ريال';
}

// دالة للحصول على جميع المنتجات
function get_all_products($conn) {
    $result = $conn->query("SELECT * FROM products ORDER BY created_at DESC");
    if ($result) {
        return $result->fetch_all(MYSQLI_ASSOC);
    } else {
        error_log("خطأ في جلب المنتجات: " . $conn->error);
        return [];
    }
}

// دالة للحصول على جميع الأقسام
function get_all_categories($conn) {
    $result = $conn->query("SELECT * FROM categories ORDER BY name");
    if ($result) {
        return $result->fetch_all(MYSQLI_ASSOC);
    } else {
        error_log("خطأ في جلب الأقسام: " . $conn->error);
        return [];
    }
}

// دالة للحصول على الأكثر مبيعاً
function get_bestsellers($conn) {
    $result = $conn->query("SELECT * FROM `most_sell` ORDER BY id");
    if ($result) {
        return $result->fetch_all(MYSQLI_ASSOC);
    } else {
        error_log("خطأ في جلب الأكثر مبيعاً: " . $conn->error);
        return [];
    }
}

// دالة للحصول على منتج واحد
function get_product_by_id($conn, $id) {
    $stmt = $conn->prepare("SELECT * FROM products WHERE id = ?");
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $result = $stmt->get_result();
    if ($result) {
        return $result->fetch_assoc();
    } else {
        error_log("خطأ في جلب المنتج: " . $conn->error);
        return false;
    }
}

// دالة لحذف منتج
function delete_product($conn, $id) {
    $stmt = $conn->prepare("DELETE FROM products WHERE id = ?");
    $stmt->bind_param("i", $id);
    if ($stmt->execute()) {
        return true;
    } else {
        error_log("خطأ في حذف المنتج: " . $conn->error);
        return false;
    }
}

// دالة لإضافة منتج جديد
function add_product($conn, $name, $description, $image, $price, $old_price, $category) {
    $stmt = $conn->prepare("INSERT INTO products (name, description, image, price, old_price, category) VALUES (?, ?, ?, ?, ?, ?)");
    $stmt->bind_param("sssdds", $name, $description, $image, $price, $old_price, $category);
    if ($stmt->execute()) {
        return true;
    } else {
        error_log("خطأ في إضافة المنتج: " . $conn->error);
        return false;
    }
}

// دالة لتحديث منتج
function update_product($conn, $id, $name, $description, $image, $price, $old_price, $category) {
    $stmt = $conn->prepare("UPDATE products SET name = ?, description = ?, image = ?, price = ?, old_price = ?, category = ? WHERE id = ?");
    $stmt->bind_param("sssddi", $name, $description, $image, $price, $old_price, $category, $id);
    if ($stmt->execute()) {
        return true;
    } else {
        error_log("خطأ في تحديث المنتج: " . $conn->error);
        return false;
    }
}

// دالة للبحث في المنتجات
function search_products($conn, $search_term, $category = null) {
    $sql = "SELECT * FROM products WHERE name LIKE ?";
    $types = "s";
    $params = ['%' . $search_term . '%'];

    if ($category) {
        $sql .= " AND category = ?";
        $types .= "s";
        $params[] = $category;
    }

    $sql .= " ORDER BY created_at DESC";

    $stmt = $conn->prepare($sql);
    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result) {
        return $result->fetch_all(MYSQLI_ASSOC);
    } else {
        error_log("خطأ في البحث: " . $conn->error);
        return [];
    }
}

// دالة للحصول على إحصائيات لوحة التحكم
function get_dashboard_stats($conn) {
    $stats = [];

    // عدد المنتجات
    $result = $conn->query("SELECT COUNT(*) as count FROM products");
    if ($result) {
        $row = $result->fetch_assoc();
        $stats['products_count'] = $row['count'];
    } else {
        $stats['products_count'] = 0;
    }

    // عدد الأقسام
    $result = $conn->query("SELECT COUNT(*) as count FROM categories");
    if ($result) {
        $row = $result->fetch_assoc();
        $stats['categories_count'] = $row['count'];
    } else {
        $stats['categories_count'] = 0;
    }

    // عدد المستخدمين
    $result = $conn->query("SELECT COUNT(*) as count FROM users");
    if ($result) {
        $row = $result->fetch_assoc();
        $stats['users_count'] = $row['count'];
    } else {
        $stats['users_count'] = 0;
    }

    return $stats;
}
?>
