# لوحة التحكم - دليل الاستخدام

## المشاكل التي تم إصلاحها

### 1. مشكلة التنقل بين الأقسام
- **المشكلة**: كان التنقل بين أقسام لوحة التحكم لا يعمل بسبب عدم وجود البيانات والدوال المطلوبة
- **الحل**: تم إضافة:
  - بيانات المنتجات التجريبية (`productsData`)
  - دالة `loadProductsTable()` لعرض المنتجات
  - معالجة أفضل للأخطاء في دالة `switchSection()`
  - حفظ حالة القسم النشط في localStorage

### 2. تحسينات إضافية
- إضافة CSS لتحسين مظهر الجدول والحالات
- معالجة الأخطاء عند عدم وجود العناصر
- إضافة التنقل بالكيبورد (Alt + 1-4)
- إغلاق الشريط الجانبي تلقائياً على الهواتف
- إضافة رسائل console للتشخيص

## كيفية الاستخدام

### التنقل بين الأقسام
1. **بالنقر**: انقر على أي قسم في الشريط الجانبي
2. **بالكيبورد**: 
   - Alt + 1: الرئيسية
   - Alt + 2: إدارة المنتجات
   - Alt + 3: الأكثر مبيعاً
   - Alt + 4: إدارة الأقسام

### الأقسام المتاحة

#### 1. الرئيسية (Dashboard)
- عرض الإحصائيات العامة
- إجمالي المنتجات وعدد الأقسام

#### 2. إدارة المنتجات (Products)
- عرض جدول المنتجات
- فلترة المنتجات حسب القسم والحالة
- البحث في المنتجات
- تعديل وحذف المنتجات

#### 3. الأكثر مبيعاً (Bestsellers)
- عرض المنتجات الأكثر مبيعاً
- ترتيب المنتجات حسب المبيعات

#### 4. إدارة الأقسام (Categories)
- عرض جميع الأقسام
- إضافة وتعديل الأقسام

## الميزات التقنية

### حفظ الحالة
- يتم حفظ القسم النشط في localStorage
- عند إعادة تحميل الصفحة، يتم العودة للقسم الأخير

### التصميم المتجاوب
- يعمل على جميع أحجام الشاشات
- شريط جانبي قابل للطي على الهواتف

### معالجة الأخطاء
- فحص وجود العناصر قبل التعامل معها
- رسائل console للتشخيص
- fallback للقسم الرئيسي عند حدوث خطأ

## البيانات التجريبية

تم إضافة 5 منتجات تجريبية تشمل:
- كريم أساس فائق التغطية
- شامبو طبيعي للشعر الجاف
- مجموعة العناية الكاملة 1
- مقشر طبيعي للوجه
- مجموعة العناية الكاملة 2

## ملاحظات للمطورين

### إضافة منتجات جديدة
```javascript
productsData.push({
    id: 6,
    name: 'اسم المنتج',
    category: 'القسم',
    price: 99.99,
    stock: 10,
    status: 'متوفر',
    image: 'img/product.jpg'
});
```

### إضافة قسم جديد
1. أضف عنصر navigation في HTML
2. أضف content section جديد
3. أضف العنوان في دالة switchSection
4. أضف معالج keyboard إذا لزم الأمر

## استكشاف الأخطاء

### إذا لم يعمل التنقل
1. تحقق من console للأخطاء
2. تأكد من وجود العناصر في HTML
3. تحقق من تطابق data-section مع id القسم

### إذا لم تظهر المنتجات
1. تحقق من وجود products-table-body في HTML
2. تأكد من تحميل البيانات بشكل صحيح
3. فحص console للأخطاء
