/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background: #f8f9fa;
    color: #333;
    direction: rtl;
    overflow-x: hidden;
}

/* Sidebar Styles */
.sidebar {
    position: fixed;
    right: 0;
    top: 0;
    width: 280px;
    height: 100vh;
    background: linear-gradient(135deg, #f8e8e8, #e8d5d5);
    box-shadow: 2px 0 15px rgba(0,0,0,0.1);
    z-index: 1000;
    transition: transform 0.3s ease;
}

.sidebar-header {
    padding: 2rem 1.5rem;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    text-align: center;
}

.sidebar-header h2 {
    color: #5a4a4a;
    font-size: 1.8rem;
    margin-bottom: 0.5rem;
}

.sidebar-header h2 i {
    color: #d4a4a4;
    margin-left: 0.5rem;
}

.sidebar-header p {
    color: #7a6a6a;
    font-size: 0.9rem;
}

.sidebar-nav ul {
    list-style: none;
    padding: 1rem 0;
}

.nav-item {
    margin-bottom: 0.5rem;
}

.nav-item a {
    display: flex;
    align-items: center;
    padding: 1rem 1.5rem;
    color: #5a4a4a;
    text-decoration: none;
    transition: all 0.3s ease;
    border-right: 3px solid transparent;
}

.nav-item a:hover,
.nav-item.active a {
    background: rgba(212, 164, 164, 0.2);
    border-right-color: #d4a4a4;
    color: #d4a4a4;
}

.nav-item a i {
    width: 20px;
    margin-left: 1rem;
    font-size: 1.1rem;
}

/* Main Content */
.main-content {
    margin-right: 280px;
    min-height: 100vh;
    background: #f8f9fa;
}

/* Admin Header */
.admin-header {
    background: white;
    padding: 1rem 2rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.sidebar-toggle {
    display: none;
    background: none;
    border: none;
    font-size: 1.2rem;
    color: #666;
    cursor: pointer;
}

.header-left h1 {
    color: #333;
    font-size: 1.5rem;
    font-weight: 600;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.search-box {
    position: relative;
}

.search-box input {
    padding: 0.5rem 2.5rem 0.5rem 1rem;
    border: 1px solid #e0e0e0;
    border-radius: 25px;
    width: 300px;
    font-family: 'Cairo', sans-serif;
    background: #f8f9fa;
}

.search-box i {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
}
.admin-profile {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
}

.admin-profile img {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    object-fit: cover;
}

/* Content Area */
.content-area {
    padding: 2rem;
}

.content-section {
    display: none;
}

.content-section.active {
    display: block;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.stat-card:nth-child(1) .stat-icon {
    background: linear-gradient(135deg, #667eea, #764ba2);
}

.stat-card:nth-child(2) .stat-icon {
    background: linear-gradient(135deg, #f093fb, #f5576c);
}

.stat-card:nth-child(3) .stat-icon {
    background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.stat-card:nth-child(4) .stat-icon {
    background: linear-gradient(135deg, #43e97b, #38f9d7);
}

.stat-info h3 {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.stat-number {
    font-size: 1.8rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 0.3rem;
}

.stat-change {
    font-size: 0.8rem;
}

.stat-change.positive {
    color: #27ae60;
}

.stat-change.negative {
    color: #e74c3c;
}

/* Section Header */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.section-header h2 {
    color: #333;
    font-size: 1.8rem;
}

/* Buttons */
.btn {
    padding: 0.7rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-family: 'Cairo', sans-serif;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
}

.btn-primary {
    background: linear-gradient(135deg, #d4a4a4, #c49494);
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #c49494, #b48484);
    transform: translateY(-1px);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
}

.btn-sm {
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
}

/* Products Filters */
.products-filters {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    align-items: center;
}

.filter-group select {
    padding: 0.5rem 1rem;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    font-family: 'Cairo', sans-serif;
    background: white;
}

.search-filter {
    position: relative;
    margin-right: auto;
}

.search-filter input {
    padding: 0.5rem 2.5rem 0.5rem 1rem;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    width: 300px;
    font-family: 'Cairo', sans-serif;
}

.search-filter i {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
}

/* Products Table */
.products-table-container {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.products-table {
    width: 100%;
    border-collapse: collapse;
}

.products-table th,
.products-table td {
    padding: 1rem;
    text-align: right;
    border-bottom: 1px solid #e0e0e0;
}

.products-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #333;
}

.products-table img {
    width: 50px;
    height: 50px;
    object-fit: cover;
    border-radius: 6px;
}

/* Best Sellers Grid */
.bestsellers-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.bestseller-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    position: relative;
    transition: transform 0.3s ease;
}

.bestseller-card:hover {
    transform: translateY(-3px);
}

.rank-badge {
    position: absolute;
    top: -10px;
    right: -10px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    font-size: 1.2rem;
}

.rank-1 .rank-badge {
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    color: #333;
}

.rank-2 .rank-badge {
    background: linear-gradient(135deg, #c0c0c0, #e8e8e8);
    color: #333;
}

.rank-3 .rank-badge {
    background: linear-gradient(135deg, #cd7f32, #daa520);
}

.bestseller-card .product-image {
    text-align: center;
    margin-bottom: 1rem;
}

.bestseller-card .product-image img {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 8px;
}

.product-details h3 {
    color: #333;
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.sales-count,
.revenue {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 0.3rem;
}

.bestseller-actions {
    margin-top: 1rem;
    text-align: center;
}

/* Categories Grid */
.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.category-card {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    transition: transform 0.3s ease;
}

.category-card:hover {
    transform: translateY(-3px);
}

.category-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.category-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.category-card:hover .category-image img {
    transform: scale(1.05);
}

.category-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.category-card:hover .category-overlay {
    opacity: 1;
}

.edit-category-btn {
    background: rgba(255,255,255,0.9);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background 0.3s ease;
}

.edit-category-btn:hover {
    background: white;
}

.category-info {
    padding: 1.5rem;
}

.category-info h3 {
    color: #333;
    margin-bottom: 0.5rem;
    font-size: 1.2rem;
}

.product-count {
    color: #d4a4a4;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.category-description {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 1rem;
    line-height: 1.5;
}

.category-status {
    display: inline-block;
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    margin-bottom: 1rem;
}

.category-status.active {
    background: #d4edda;
    color: #155724;
}

.category-actions {
    display: flex;
    gap: 0.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(100%);
        width: 100%;
    }
    
    .sidebar.active {
        transform: translateX(0);
    }
    
    .main-content {
        margin-right: 0;
    }
    
    .sidebar-toggle {
        display: block;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .dashboard-charts {
        grid-template-columns: 1fr;
    }
    
    .search-box input,
    .search-filter input {
        width: 200px;
    }
    
    .products-filters {
        flex-direction: column;
        align-items: stretch;
    }
    
    .search-filter {
        margin-right: 0;
    }
    
    .bestsellers-grid,
    .categories-grid {
        grid-template-columns: 1fr;
    }
}
