/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    line-height: 1.6;
    color: #333;
    direction: rtl;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background: linear-gradient(135deg, #f8e8e8, #e8d5d5);
    color: #5a4a4a;
    padding: 1rem 0;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    box-shadow: 0 2px 15px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
}

.header.scrolled {
    background: rgba(248, 232, 232, 0.95);
    backdrop-filter: blur(10px);
    padding: 0.8rem 0;
}

.nav-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
}

.logo h1 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0;
}

.logo span {
    font-size: 0.9rem;
    opacity: 0.9;
}

.nav ul {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav a {
    color: #5a4a4a;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s;
}

.nav a:hover {
    color: #d4a4a4;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.search-box {
    position: relative;
}

.search-box input {
    padding: 0.5rem 2.5rem 0.5rem 1rem;
    border: none;
    border-radius: 25px;
    width: 250px;
    font-family: 'Cairo', sans-serif;
}

.search-box i {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
}

.cart {
    position: relative;
    cursor: pointer;
    font-size: 1.2rem;
}

.cart-count {
    position: absolute;
    top: -8px;
    left: -8px;
    background: #fff;
    color: #ff6b9d;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    font-weight: bold;
}

.mobile-menu-toggle {
    display: none;
    font-size: 1.5rem;
    cursor: pointer;
}

/* Currency Switcher */
.currency-switcher {
    display: flex;
    align-items: center;
    margin-left: 1rem;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(255,107,157,0.08);
    padding: 0.2rem 0.7rem;
    height: 38px;
}

.currency-switcher select {
    border: none;
    background: transparent;
    font-family: 'Cairo', sans-serif;
    font-size: 1rem;
    color: #ff2a6d;
    font-weight: bold;
    outline: none;
    cursor: pointer;
    padding: 0 0.5rem;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    direction: rtl;
}

.currency-switcher select:focus {
    box-shadow: 0 0 0 2px #ff2a6d33;
}

.currency-switcher::after {
    content: '\f078';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    color: #ff2a6d;
    margin-right: 0.5rem;
    pointer-events: none;
    font-size: 0.9em;
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, #fdf9f9 0%, #f9f4f4 50%, #f5eeee 100%);
    background-attachment: fixed;
    padding: 140px 0 100px;
    margin-top: 15px;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="%23d4a4a4" opacity="0.05"/><circle cx="75" cy="75" r="1" fill="%23c49999" opacity="0.05"/><circle cx="50" cy="10" r="0.5" fill="%23d4a4a4" opacity="0.03"/><circle cx="10" cy="50" r="0.5" fill="%23c49999" opacity="0.03"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
    z-index: 1;
}

/* Background Elements */
.hero-bg-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.floating-element {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(212, 164, 164, 0.1), rgba(196, 153, 153, 0.05));
    animation: float 6s ease-in-out infinite;
}

.element-1 {
    width: 80px;
    height: 80px;
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.element-2 {
    width: 120px;
    height: 120px;
    top: 60%;
    right: 15%;
    animation-delay: 2s;
}

.element-3 {
    width: 60px;
    height: 60px;
    top: 80%;
    left: 20%;
    animation-delay: 4s;
}

.element-4 {
    width: 100px;
    height: 100px;
    top: 10%;
    right: 25%;
    animation-delay: 1s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 5rem;
    align-items: center;
    min-height: 600px;
    position: relative;
    z-index: 2;
}

/* Hero Text Styling */
.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: linear-gradient(135deg, rgba(212, 164, 164, 0.15), rgba(196, 153, 153, 0.1));
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.9rem;
    color: #8a6b6b;
    margin-bottom: 1.5rem;
    border: 1px solid rgba(212, 164, 164, 0.2);
}

.hero-badge i {
    color: #7fb069;
    font-size: 1rem;
}

.hero-title {
    font-size: 3.5rem;
    color: #2c3e50;
    margin-bottom: 1.5rem;
    line-height: 1.1;
    font-weight: 700;
}

.title-line-1 {
    display: block;
    background: linear-gradient(135deg, #d4a4a4, #c49999);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.title-line-2 {
    display: block;
    color: #2c3e50;
}

.hero-description {
    font-size: 1.2rem;
    color: #7f8c8d;
    margin-bottom: 2rem;
    line-height: 1.7;
    max-width: 90%;
}

/* Hero Features */
.hero-features {
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
    margin-bottom: 2.5rem;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    font-size: 1rem;
    color: #5a4a4a;
}

.feature-item i {
    color: #7fb069;
    font-size: 1.1rem;
}

/* Hero Buttons */
.hero-buttons {
    display: flex;
    gap: 1rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.cta-button {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    border: none;
    padding: 1rem 2rem;
    font-size: 1.1rem;
    border-radius: 30px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: 'Cairo', sans-serif;
    font-weight: 600;
    text-decoration: none;
}

.cta-button.primary {
    background: linear-gradient(135deg, #d4a4a4, #c49999);
    color: white;
}

.cta-button.primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(212, 164, 164, 0.4);
}

.cta-button.secondary {
    background: transparent;
    color: #d4a4a4;
    border: 2px solid #d4a4a4;
}

.cta-button.secondary:hover {
    background: #d4a4a4;
    color: white;
    transform: translateY(-2px);
}

/* Hero Stats */
.hero-stats {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 1.8rem;
    font-weight: 700;
    color: #d4a4a4;
    margin-bottom: 0.2rem;
}

.stat-label {
    font-size: 0.9rem;
    color: #8a7a7a;
}

/* Hero Image Styling */
.hero-image {
    position: relative;
    z-index: 2;
}

.image-container {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

.image-decoration {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.decoration-circle {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(212, 164, 164, 0.2), rgba(196, 153, 153, 0.1));
    animation: rotate 10s linear infinite;
}

.circle-1 {
    width: 100px;
    height: 100px;
    top: -20px;
    right: -20px;
    animation-delay: 0s;
}

.circle-2 {
    width: 80px;
    height: 80px;
    bottom: -10px;
    left: -30px;
    animation-delay: 3s;
}

.circle-3 {
    width: 60px;
    height: 60px;
    top: 50%;
    right: -40px;
    animation-delay: 6s;
}

@keyframes rotate {
    0% { transform: rotate(0deg) scale(1); }
    50% { transform: rotate(180deg) scale(1.1); }
    100% { transform: rotate(360deg) scale(1); }
}

.hero-image img {
    width: 100%;
    max-width: 500px;
    height: 500px;
    object-fit: cover;
    border-radius: 25px;
    box-shadow: 0 25px 50px rgba(0,0,0,0.15);
    position: relative;
    z-index: 2;
    transition: transform 0.3s ease;
}

.hero-image img:hover {
    transform: scale(1.02);
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 3;
}

.floating-product {
    position: absolute;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: white;
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    display: flex;
    align-items: center;
    justify-content: center;
    animation: bounce 3s ease-in-out infinite;
}

.floating-product img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
    box-shadow: none;
}

.product-1 {
    top: 15%;
    left: -10%;
    animation-delay: 0s;
}

.product-2 {
    bottom: 20%;
    right: -8%;
    animation-delay: 1.5s;
}

@keyframes bounce {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-15px); }
}

/* Ripple Effect for Buttons */
.cta-button {
    position: relative;
    overflow: hidden;
}

.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.6);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* Enhanced Hero Animations */
.hero-badge {
    animation: slideInFromTop 1s ease-out;
}

.hero-title {
    animation: slideInFromLeft 1s ease-out 0.2s both;
}

.hero-description {
    animation: slideInFromLeft 1s ease-out 0.4s both;
}

.hero-features {
    animation: slideInFromLeft 1s ease-out 0.6s both;
}

.hero-buttons {
    animation: slideInFromLeft 1s ease-out 0.8s both;
}

.hero-stats {
    animation: slideInFromLeft 1s ease-out 1s both;
}

.hero-image {
    animation: slideInFromRight 1s ease-out 0.5s both;
}

@keyframes slideInFromTop {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInFromLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInFromRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Section Titles */
.section-title {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 3rem;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(135deg, #d4a4a4, #c49999);
    border-radius: 2px;
}

/* Care Categories Slider Section */
.care-categories {
    padding: 80px 0;
    background: linear-gradient(135deg, #faf7f7, #f5f0f0);
    overflow: hidden;
}

.categories-slider-container {
    margin-top: 3rem;
    position: relative;
    max-width: 100%;
    overflow: hidden;
}

.categories-swiper {
    padding: 20px 0 60px;
    overflow: visible;
}

.categories-swiper .swiper-wrapper {
    align-items: stretch;
}

.categories-swiper .swiper-slide {
    height: auto;
    display: flex;
    align-items: stretch;
}

.category-slide {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0,0,0,0.08);
    transition: all 0.4s ease;
    height: 500px;
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100%;
}

.category-slide:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.12);
}

.category-image {
    position: relative;
    height: 280px;
    overflow: hidden;
    flex-shrink: 0;
}

.category-image img {
    width: 100%;
    height: 100%;
    object-position: center;
    transition: transform 0.4s ease;
    display: block;
}

.category-slide:hover .category-image img {
    transform: scale(1.05);
}

.category-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, transparent 0%, rgba(212, 164, 164, 0.1) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.category-slide:hover .category-overlay {
    opacity: 1;
}

.category-content {
    padding: 1.5rem;
    text-align: center;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    min-height: 170px;
}

.category-content h3 {
    font-size: 1.4rem;
    color: #5a4a4a;
    margin-bottom: 0.8rem;
    font-weight: 600;
    flex-shrink: 0;
}

.category-content p {
    color: #8a7a7a;
    font-size: 0.95rem;
    line-height: 1.6;
    margin-bottom: 1.2rem;
    flex-grow: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
}

.category-btn {
    background: linear-gradient(135deg, #d4a4a4, #c49999);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 25px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: 'Cairo', sans-serif;
    flex-shrink: 0;
    align-self: center;
    min-width: 140px;
}

.category-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(212, 164, 164, 0.3);
}

.category-btn.special-offer {
    background: linear-gradient(135deg, #e8b4b4, #d49999);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(228, 180, 180, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(228, 180, 180, 0); }
    100% { box-shadow: 0 0 0 0 rgba(228, 180, 180, 0); }
}

/* Swiper Navigation Buttons */
.categories-swiper .swiper-button-next,
.categories-swiper .swiper-button-prev {
    color: #d4a4a4;
    background: white;
    width: 45px;
    height: 45px;
    border-radius: 50%;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.categories-swiper .swiper-button-next:hover,
.categories-swiper .swiper-button-prev:hover {
    background: #d4a4a4;
    color: white;
    transform: scale(1.1);
}

.categories-swiper .swiper-button-next::after,
.categories-swiper .swiper-button-prev::after {
    font-size: 16px;
    font-weight: bold;
}

/* Swiper Pagination */
.categories-swiper .swiper-pagination-bullet {
    background: #d4a4a4;
    opacity: 0.3;
    width: 12px;
    height: 12px;
    transition: all 0.3s ease;
}

.categories-swiper .swiper-pagination-bullet-active {
    opacity: 1;
    transform: scale(1.2);
    background: #c49999;
}

/* Featured Products */
.featured-products {
    padding: 80px 0;
    background: linear-gradient(135deg, #fefcfc, #faf7f7);
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.product-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: transform 0.3s, box-shadow 0.3s;
    opacity: 0;
    transform: translateY(30px);
}

.product-card.animate-in {
    opacity: 1;
    transform: translateY(0);
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
}

.product-image {
    position: relative;
    overflow: hidden;
}

.product-image img {
    width: 100%;
    height: 310px;
    transition: transform 0.3s;
}

.product-card:hover .product-image img {
    transform: scale(1.05);
}

.product-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 107, 157, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s;
}

.product-card:hover .product-overlay {
    opacity: 1;
}

.add-to-cart {
    background: white;
    color: #d4a4a4;
    border: none;
    padding: 0.8rem 1.5rem;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: transform 0.3s;
    font-family: 'Cairo', sans-serif;
}

.add-to-cart:hover {
    transform: scale(1.05);
}

.product-info {
    padding: 1.5rem;
}

.product-info h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.rating {
    margin-bottom: 1rem;
}

.rating i {
    color: #ffd700;
    font-size: 0.9rem;
}

.price {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.current-price {
    font-size: 1.3rem;
    font-weight: 700;
    color: #ff6b9d;
}

.old-price {
    font-size: 1rem;
    color: #999;
    text-decoration: line-through;
}

/* Best Sellers */
.best-sellers {
    padding: 80px 0;
    background: #fff;
}

.bestseller-grid {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    max-width: 600px;
    margin: 0 auto;
}

.bestseller-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    background: white;
    padding: 1rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s, box-shadow 0.3s;
}

.bestseller-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.bestseller-rank {
    background: linear-gradient(135deg, #ff6b9d, #c44569);
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1.2rem;
}

.bestseller-item img {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 8px;
}

.bestseller-info {
    flex: 1;
}

.bestseller-info h4 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.bestseller-price {
    font-size: 1.2rem;
    font-weight: 700;
    color: #ff6b9d;
}

/* About Section */
.about {
    padding: 80px 0;
    background: #fff;
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
}

.about-text h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1.5rem;
}

.about-text p {
    font-size: 1.1rem;
    color: #666;
    margin-bottom: 2rem;
    line-height: 1.8;
}

.about-text ul {
    list-style: none;
}

.about-text li {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    font-size: 1.1rem;
    color: #555;
}

.about-text li i {
    color: #ff6b9d;
    font-size: 1.2rem;
}

.about-image img {
    width: 100%;
    height: 550px;
    object-fit: cover;
    border-radius: 15px;
    box-shadow: 43px 14px 64px 19px rgba(0, 0, 0, 0.1);
}

/* Contact Section */
.contact {
    padding: 80px 0;
    background: #f8f9fa;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.contact-item i {
    font-size: 1.5rem;
    color: #ff6b9d;
    width: 40px;
    text-align: center;
}

.contact-item h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.contact-item p {
    color: #666;
    font-size: 1rem;
}

.contact-form {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.contact-form input,
.contact-form textarea {
    width: 100%;
    padding: 1rem;
    margin-bottom: 1rem;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    font-family: 'Cairo', sans-serif;
    font-size: 1rem;
    transition: border-color 0.3s;
}

.contact-form input:focus,
.contact-form textarea:focus {
    outline: none;
    border-color: #ff6b9d;
}

.contact-form button {
    background: linear-gradient(135deg, #ff6b9d, #c44569);
    color: white;
    border: none;
    padding: 1rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 10px;
    cursor: pointer;
    transition: transform 0.3s;
    font-family: 'Cairo', sans-serif;
    width: 100%;
}

.contact-form button:hover {
    transform: translateY(-2px);
}

/* Footer */
.footer {
    background: #2c3e50;
    color: white;
    padding: 60px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #ff6b9d;
}

.footer-section p {
    color: #bdc3c7;
    line-height: 1.6;
    margin-bottom: 1rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: #bdc3c7;
    text-decoration: none;
    transition: color 0.3s;
}

.footer-section ul li a:hover {
    color: #ff6b9d;
}

.social-links {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.social-links a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: #34495e;
    color: white;
    border-radius: 50%;
    text-decoration: none;
    transition: background 0.3s, transform 0.3s;
}

.social-links a:hover {
    background: #ff6b9d;
    transform: translateY(-2px);
}

.newsletter {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
}

.newsletter input {
    flex: 1;
    padding: 0.8rem;
    border: none;
    border-radius: 5px;
    font-family: 'Cairo', sans-serif;
}

.newsletter button {
    background: #ff6b9d;
    color: white;
    border: none;
    padding: 0.8rem 1.5rem;
    border-radius: 5px;
    cursor: pointer;
    font-family: 'Cairo', sans-serif;
    font-weight: 600;
    transition: background 0.3s;
}

.newsletter button:hover {
    background: #c44569;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #34495e;
    color: #bdc3c7;
}

/* Cart Modal Styles */
.cart-modal {
    display: none;
    position: fixed;
    z-index: 2000;
    right: 0;
    top: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0,0,0,0.35);
    align-items: center;
    justify-content: center;
}
.cart-modal-content {
    background: #fff;
    border-radius: 18px;
    max-width: 400px;
    width: 90%;
    margin: 60px auto 0 auto;
    padding: 2rem 1.5rem 1.5rem 1.5rem;
    box-shadow: 0 8px 40px rgba(0,0,0,0.18);
    position: relative;
    animation: fadeInCart 0.3s;
}
@keyframes fadeInCart {
    from { transform: translateY(-40px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}
.close-modal {
    position: absolute;
    top: 12px;
    left: 18px;
    font-size: 1.5rem;
    color: #ff6b9d;
    cursor: pointer;
    font-weight: bold;
}
.cart-modal h3 {
    text-align: center;
    margin-bottom: 1.2rem;
    color: #2c3e50;
}
.cart-items {
    list-style: none;
    padding: 0;
    margin-bottom: 1.2rem;
    max-height: 220px;
    overflow-y: auto;
}
.cart-items li {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 0.5rem;
    padding: 0.5rem 0;
    border-bottom: 1px solid #eee;
    font-size: 1rem;
}
.cart-qty-btn {
    background: #f3f3f3;
    border: none;
    color: #ff6b9d;
    font-size: 1.1rem;
    width: 28px;
    height: 28px;
    border-radius: 50%;
    cursor: pointer;
    font-weight: bold;
    transition: background 0.2s, color 0.2s;
    margin: 0 2px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.cart-qty-btn:hover {
    background: #ff6b9d;
    color: #fff;
}
.cart-qty {
    min-width: 22px;
    display: inline-block;
    text-align: center;
    font-weight: bold;
    color: #2c3e50;
}
.cart-remove-btn {
    background: none;
    border: none;
    color: #ff6b9d;
    font-size: 1.2rem;
    cursor: pointer;
    margin-right: 6px;
    transition: color 0.2s;
}
.cart-remove-btn:hover {
    color: #c44569;
}

.whatsapp-checkout {
    background: linear-gradient(135deg, #25d366, #128c7e);
    color: #fff;
    border: none;
    padding: 0.9rem 1.5rem;
    border-radius: 25px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    width: 100%;
    transition: background 0.3s;
    font-family: 'Cairo', sans-serif;
    margin-top: 1rem;
    display: block;
    box-shadow: 0 2px 10px rgba(37,211,102,0.08);
    letter-spacing: 1px;
}
.whatsapp-checkout:hover {
    background: linear-gradient(135deg, #128c7e, #25d366);
    color: #fff;
    box-shadow: 0 4px 18px rgba(37,211,102,0.18);
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav {
        display: none;
    }

    .mobile-menu-toggle {
        display: block;
    }

    .header-actions .search-box {
        display: none;
    }

    /* Hero Mobile Styles */
    .hero {
        padding: 120px 0 80px;
    }

    .hero-content {
        grid-template-columns: 1fr;
        gap: 3rem;
        text-align: center;
        min-height: auto;
    }

    .hero-title {
        font-size: 2.8rem;
        margin-bottom: 1rem;
    }

    .hero-description {
        font-size: 1.1rem;
        max-width: 100%;
        margin-bottom: 1.5rem;
    }

    .hero-features {
        margin-bottom: 2rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
        gap: 1rem;
        margin-bottom: 2rem;
    }

    .cta-button {
        width: 100%;
        max-width: 280px;
        justify-content: center;
    }

    .hero-stats {
        justify-content: center;
        gap: 1.5rem;
    }

    .stat-number {
        font-size: 1.5rem;
    }

    .hero-image img {
        height: 400px;
        max-width: 100%;
    }

    .floating-product {
        width: 60px;
        height: 60px;
    }

    .floating-product img {
        width: 45px;
        height: 45px;
    }

    .decoration-circle {
        display: none;
    }

    .floating-element {
        display: none;
    }

    .categories-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }

    .products-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }

    .offer-banner {
        grid-template-columns: 1fr;
    }

    .offer-timer {
        justify-content: center;
        gap: 0.5rem;
    }

    .timer-item {
        min-width: 50px;
        padding: 0.8rem;
    }

    .timer-number {
        font-size: 1.2rem;
    }

    .bestseller-grid {
        max-width: 100%;
    }

    .bestseller-item {
        padding: 0.8rem;
    }

    .bestseller-rank {
        width: 35px;
        height: 35px;
        font-size: 1rem;
    }

    .bestseller-item img {
        width: 50px;
        height: 50px;
    }

    .about-content {
        grid-template-columns: 1fr;
    }

    .contact-content {
        grid-template-columns: 1fr;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .newsletter {
        flex-direction: column;
    }

    /* Categories Slider Mobile Adjustments */
    .categories-swiper {
        padding: 10px 0 50px;
    }

    .category-slide {
        height: 380px;
        margin: 0 auto;
        max-width: 300px;
    }

    .category-image {
        height: 220px;
    }

    .category-content {
        min-height: 160px;
        padding: 1.2rem;
    }

    .category-content h3 {
        font-size: 1.3rem;
        margin-bottom: 0.6rem;
    }

    .category-content p {
        font-size: 0.85rem;
        line-height: 1.5;
        margin-bottom: 1rem;
    }

    .category-btn {
        padding: 0.8rem 1.5rem;
        font-size: 0.9rem;
        min-width: 120px;
    }

    .categories-swiper .swiper-button-next,
    .categories-swiper .swiper-button-prev {
        width: 40px;
        height: 40px;
        margin-top: -20px;
    }

    .categories-swiper .swiper-button-next::after,
    .categories-swiper .swiper-button-prev::after {
        font-size: 15px;
    }

    .categories-swiper .swiper-pagination {
        bottom: 10px;
    }

    .categories-swiper .swiper-pagination-bullet {
        width: 10px;
        height: 10px;
        margin: 0 4px;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }

    .hero {
        padding: 100px 0 60px;
    }

    .hero-text h2 {
        font-size: 1.8rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .categories-grid,
    .products-grid {
        grid-template-columns: 1fr;
    }

    .offer-content {
        padding: 2rem;
    }

    .offer-content h2 {
        font-size: 2rem;
    }
}
