# ربط لوحة التحكم بقاعدة البيانات - دليل شامل

## التحديثات المنجزة ✅

### 1. إنشاء نظام قاعدة البيانات
- **ملف الاتصال**: `includes/db_connection.php`
- **معالج العمليات**: `admin/process_actions.php`
- **قاعدة البيانات**: `anaqati_db` مع الجداول:
  - `products` - المنتجات
  - `categories` - الأقسام
  - `users` - المستخدمين
  - `most-sell` - الأكثر مبيعاً

### 2. تحويل لوحة التحكم من JavaScript إلى PHP
- ✅ إزالة الاعتماد على JSON/AJAX
- ✅ استخدام PHP لجلب البيانات من قاعدة البيانات
- ✅ تحديث التنقل ليستخدم GET parameters
- ✅ إضافة نماذج HTML للإضافة والتعديل
- ✅ معالجة رفع الصور

### 3. الميزات الجديدة

#### أ) إدارة المنتجات
- عرض المنتجات من قاعدة البيانات
- إضافة منتج جديد مع رفع صورة
- تعديل المنتجات الموجودة
- حذف المنتجات
- فلترة المنتجات حسب القسم
- البحث في المنتجات

#### ب) إدارة الأقسام
- عرض الأقسام من قاعدة البيانات
- إضافة قسم جديد مع صورة
- تعديل الأقسام
- حذف الأقسام
- حساب عدد المنتجات في كل قسم

#### ج) الإحصائيات الحية
- عدد المنتجات الفعلي
- عدد الأقسام
- عدد المستخدمين
- الأكثر مبيعاً من قاعدة البيانات

#### د) نظام الرسائل
- رسائل نجاح العمليات
- رسائل الأخطاء
- إخفاء تلقائي للرسائل

## بنية الملفات الجديدة

```
e-comm/
├── includes/
│   └── db_connection.php          # اتصال قاعدة البيانات والدوال
├── admin/
│   └── process_actions.php        # معالجة العمليات (CRUD)
├── admin-dashboard.php            # لوحة التحكم المحدثة
├── css/
│   └── admin-dashboard.css        # تحديثات التصميم
└── img/                          # مجلد الصور المرفوعة
```

## كيفية الاستخدام

### 1. إعداد قاعدة البيانات
```sql
-- تشغيل ملف db/anaqati_db.sql في phpMyAdmin أو MySQL
-- أو استخدام الأمر:
mysql -u root -p < db/anaqati_db.sql
```

### 2. تحديث إعدادات قاعدة البيانات
في ملف `includes/db_connection.php`:
```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'anaqati_db');
define('DB_USER', 'root');
define('DB_PASS', '');
```

### 3. التنقل في لوحة التحكم
- **الرئيسية**: `?section=dashboard`
- **المنتجات**: `?section=products`
- **الأكثر مبيعاً**: `?section=bestsellers`
- **الأقسام**: `?section=categories`

### 4. العمليات المتاحة

#### إدارة المنتجات:
- **إضافة**: انقر "إضافة منتج جديد"
- **تعديل**: انقر أيقونة التعديل في الجدول
- **حذف**: انقر أيقونة الحذف مع تأكيد
- **فلترة**: استخدم قائمة الأقسام
- **بحث**: اكتب في مربع البحث

#### إدارة الأقسام:
- **إضافة**: انقر "إضافة قسم جديد"
- **تعديل**: انقر "تعديل" في بطاقة القسم
- **حذف**: انقر "حذف" مع تأكيد

## الدوال المتاحة في db_connection.php

### دوال المنتجات:
- `get_all_products($pdo)` - جلب جميع المنتجات
- `get_product_by_id($pdo, $id)` - جلب منتج واحد
- `add_product($pdo, ...)` - إضافة منتج
- `update_product($pdo, ...)` - تحديث منتج
- `delete_product($pdo, $id)` - حذف منتج
- `search_products($pdo, $term, $category)` - البحث

### دوال الأقسام:
- `get_all_categories($pdo)` - جلب جميع الأقسام

### دوال الإحصائيات:
- `get_dashboard_stats($pdo)` - إحصائيات لوحة التحكم

### دوال مساعدة:
- `sanitize_input($data)` - تنظيف البيانات
- `format_price($price)` - تنسيق السعر
- `format_arabic_date($date)` - تنسيق التاريخ

## معالجة رفع الصور

### المجلدات المطلوبة:
- `img/` - لحفظ الصور المرفوعة

### الأنواع المدعومة:
- JPG, JPEG, PNG, GIF, WEBP
- الحد الأقصى: 5MB

### آلية العمل:
1. التحقق من نوع الملف
2. التحقق من الحجم
3. إنشاء اسم فريد
4. نقل الملف إلى المجلد
5. حفظ المسار في قاعدة البيانات

## الأمان

### تدابير الحماية المطبقة:
- ✅ استخدام Prepared Statements
- ✅ تنظيف البيانات المدخلة
- ✅ التحقق من أنواع الملفات
- ✅ تحديد حجم الملفات
- ✅ استخدام htmlspecialchars لمنع XSS

### توصيات إضافية:
- إضافة نظام تسجيل دخول
- تشفير كلمات المرور
- إضافة CSRF tokens
- تحديد صلاحيات المستخدمين

## استكشاف الأخطاء

### مشاكل شائعة وحلولها:

#### 1. خطأ اتصال قاعدة البيانات:
```
خطأ في الاتصال بقاعدة البيانات
```
**الحل**: تحقق من إعدادات قاعدة البيانات في `db_connection.php`

#### 2. عدم ظهور الصور:
**الحل**: تأكد من وجود مجلد `img/` وصلاحيات الكتابة

#### 3. عدم عمل رفع الصور:
**الحل**: تحقق من إعدادات PHP:
```php
upload_max_filesize = 5M
post_max_size = 5M
```

#### 4. عدم ظهور البيانات:
**الحل**: تحقق من وجود البيانات في قاعدة البيانات وتشغيل ملف SQL

## التطوير المستقبلي

### ميزات مقترحة:
- [ ] نظام تسجيل الدخول
- [ ] إدارة الطلبات
- [ ] تقارير المبيعات
- [ ] إدارة المخزون
- [ ] نظام الإشعارات
- [ ] تصدير البيانات
- [ ] نسخ احتياطية تلقائية

### تحسينات تقنية:
- [ ] إضافة AJAX للعمليات السريعة
- [ ] تحسين الأداء مع فهرسة قاعدة البيانات
- [ ] إضافة cache للبيانات
- [ ] تحسين التصميم المتجاوب

## الدعم

للحصول على المساعدة:
1. تحقق من ملفات الـ log
2. استخدم Developer Tools في المتصفح
3. تحقق من error_log في PHP
4. راجع هذا الدليل للحلول الشائعة
