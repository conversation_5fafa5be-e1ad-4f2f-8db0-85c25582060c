<?php
session_start();
require_once 'includes/db_connection.php';

// الحصول على البيانات من قاعدة البيانات
$products = get_all_products($pdo);
$categories = get_all_categories($pdo);
$bestsellers = get_bestsellers($pdo);
$stats = get_dashboard_stats($pdo);

// معالجة الفلترة والبحث
$filtered_products = $products;
$search_term = $_GET['search'] ?? '';
$category_filter = $_GET['category'] ?? '';

if (!empty($search_term) || !empty($category_filter)) {
    $filtered_products = search_products($pdo, $search_term, $category_filter);
}

// الحصول على القسم النشط
$active_section = $_GET['section'] ?? 'dashboard';
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - إدارة المنتجات</title>
    <link rel="stylesheet" href="css/admin-dashboard.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h2><i class="fas fa-crown"></i> أناقتي</h2>
            <p>لوحة التحكم</p>
        </div>
        
        <nav class="sidebar-nav">
            <ul>
                <li class="nav-item <?php echo $active_section === 'dashboard' ? 'active' : ''; ?>">
                    <a href="?section=dashboard">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>الرئيسية</span>
                    </a>
                </li>
                <li class="nav-item <?php echo $active_section === 'products' ? 'active' : ''; ?>">
                    <a href="?section=products">
                        <i class="fas fa-box"></i>
                        <span>إدارة المنتجات</span>
                    </a>
                </li>
                <li class="nav-item <?php echo $active_section === 'bestsellers' ? 'active' : ''; ?>">
                    <a href="?section=bestsellers">
                        <i class="fas fa-fire"></i>
                        <span>الأكثر مبيعاً</span>
                    </a>
                </li>
                <li class="nav-item <?php echo $active_section === 'categories' ? 'active' : ''; ?>">
                    <a href="?section=categories">
                        <i class="fas fa-tags"></i>
                        <span>الأقسام</span>
                    </a>
                </li>
            </ul>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Header -->
        <header class="admin-header">
            <div class="header-left">
                <button class="sidebar-toggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1 id="page-title">
                    <?php
                    $titles = [
                        'dashboard' => 'الرئيسية',
                        'products' => 'إدارة المنتجات',
                        'bestsellers' => 'الأكثر مبيعاً',
                        'categories' => 'إدارة الأقسام'
                    ];
                    echo $titles[$active_section] ?? 'لوحة التحكم';
                    ?>
                </h1>
            </div>
            <div class="header-right">
                <div class="search-box">
                    <input type="text" placeholder="البحث...">
                    <i class="fas fa-search"></i>
                </div>
                <div class="admin-profile">
                    <img src="img/admin-avatar.jpg" alt="المدير" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiNkNGE0YTQiLz4KPHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDEyQzE0LjIwOTEgMTIgMTYgMTAuMjA5MSAxNiA4QzE2IDUuNzkwODYgMTQuMjA5MSA0IDEyIDRDOS43OTA4NiA0IDggNS43OTA4NiA4IDhDOCAxMC4yMDkxIDkuNzkwODYgMTIgMTIgMTJaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNMTIgMTRDOC42ODYyOSAxNCA2IDE2LjY4NjMgNiAyMFYyMkgxOFYyMEMxOCAxNi42ODYzIDE1LjMxMzcgMTQgMTIgMTRaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4KPC9zdmc+'">
                    <span>المدير</span>
                </div>
            </div>
        </header>

        <!-- رسائل النجاح والخطأ -->
        <?php if (isset($_SESSION['admin_message'])): ?>
            <div class="alert alert-<?php echo $_SESSION['admin_message_type']; ?>">
                <i class="fas fa-<?php echo $_SESSION['admin_message_type'] === 'success' ? 'check-circle' : 'exclamation-circle'; ?>"></i>
                <?php echo $_SESSION['admin_message']; ?>
                <button class="alert-close" onclick="this.parentElement.style.display='none'">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <?php
            unset($_SESSION['admin_message']);
            unset($_SESSION['admin_message_type']);
            ?>
        <?php endif; ?>

        <!-- Dashboard Content -->
        <div class="content-area">
            <!-- Dashboard Section -->
            <section id="dashboard-section" class="content-section <?php echo $active_section === 'dashboard' ? 'active' : ''; ?>">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-box"></i>
                        </div>
                        <div class="stat-info">
                            <h3>إجمالي المنتجات</h3>
                            <p class="stat-number"><?php echo $stats['products_count']; ?></p>
                            <span class="stat-change positive">منتج متاح</span>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-tags"></i>
                        </div>
                        <div class="stat-info">
                            <h3>عدد الأقسام</h3>
                            <p class="stat-number"><?php echo $stats['categories_count']; ?></p>
                            <span class="stat-change">قسم نشط</span>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-info">
                            <h3>عدد المستخدمين</h3>
                            <p class="stat-number"><?php echo $stats['users_count']; ?></p>
                            <span class="stat-change">مستخدم مسجل</span>
                        </div>
                    </div>
                </div>

            </section>

            <!-- Products Section -->
            <section id="products-section" class="content-section <?php echo $active_section === 'products' ? 'active' : ''; ?>">
                <div class="section-header">
                    <h2>إدارة المنتجات</h2>
                    <button class="btn btn-primary" onclick="openModal('product-modal')">
                        <i class="fas fa-plus"></i>
                        إضافة منتج جديد
                    </button>
                </div>

                <div class="products-filters">
                    <form method="GET" class="filter-form">
                        <input type="hidden" name="section" value="products">

                        <div class="filter-group">
                            <label for="category-filter">فلترة حسب القسم:</label>
                            <select name="category" id="category-filter" onchange="this.form.submit()">
                                <option value="">جميع الأقسام</option>
                                <?php foreach ($categories as $category): ?>
                                    <option value="<?php echo htmlspecialchars($category['name']); ?>"
                                            <?php echo $category_filter === $category['name'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($category['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="filter-group">
                            <label for="product-search">البحث:</label>
                            <input type="text" name="search" id="product-search"
                                   value="<?php echo htmlspecialchars($search_term); ?>"
                                   placeholder="ابحث عن منتج...">
                            <button type="submit" class="btn btn-secondary">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>

                        <?php if (!empty($search_term) || !empty($category_filter)): ?>
                            <div class="filter-group">
                                <a href="?section=products" class="btn btn-outline">
                                    <i class="fas fa-times"></i>
                                    إلغاء الفلترة
                                </a>
                            </div>
                        <?php endif; ?>
                    </form>
                </div>

                <div class="products-table-container">
                    <table class="products-table">
                        <thead>
                            <tr>
                                <th>الصورة</th>
                                <th>اسم المنتج</th>
                                <th>القسم</th>
                                <th>السعر</th>
                                <th>المخزون</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="products-table-body">
                            <?php if (empty($filtered_products)): ?>
                                <tr>
                                    <td colspan="7" class="text-center">
                                        <i class="fas fa-box-open"></i>
                                        <p>لا توجد منتجات متاحة</p>
                                    </td>
                                </tr>
                            <?php else: ?>
                                <?php foreach ($filtered_products as $product): ?>
                                    <tr>
                                        <td>
                                            <img src="<?php echo $product['image'] ?: 'img/default-product.jpg'; ?>"
                                                 alt="<?php echo htmlspecialchars($product['name']); ?>"
                                                 class="product-image"
                                                 onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjZjBmMGYwIi8+CjxwYXRoIGQ9Ik0xMiAxNkwyOCAyNE0yOCAxNkwxMiAyNCIgc3Ryb2tlPSIjY2NjIiBzdHJva2Utd2lkdGg9IjIiLz4KPC9zdmc+'">
                                        </td>
                                        <td><?php echo htmlspecialchars($product['name']); ?></td>
                                        <td><?php echo htmlspecialchars($product['category']); ?></td>
                                        <td><?php echo format_price($product['price']); ?></td>
                                        <td>
                                            <?php if ($product['old_price']): ?>
                                                <span class="old-price"><?php echo format_price($product['old_price']); ?></span>
                                            <?php else: ?>
                                                -
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="status-badge available">متوفر</span>
                                        </td>
                                        <td>
                                            <div class="action-buttons">
                                                <button class="btn btn-sm btn-primary"
                                                        onclick="editProduct(<?php echo $product['id']; ?>)">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="btn btn-sm btn-danger"
                                                        onclick="deleteProduct(<?php echo $product['id']; ?>)">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </section>

            <!-- Best Sellers Section -->
            <section id="bestsellers-section" class="content-section <?php echo $active_section === 'bestsellers' ? 'active' : ''; ?>">
                <div class="section-header">
                    <h2>الأكثر مبيعاً</h2>
                    <a href="?section=bestsellers" class="btn btn-secondary">
                        <i class="fas fa-sync-alt"></i>
                        تحديث القائمة
                    </a>
                </div>

                <div class="bestsellers-grid">
                    <?php if (empty($bestsellers)): ?>
                        <div class="empty-state">
                            <i class="fas fa-fire"></i>
                            <p>لا توجد منتجات في قائمة الأكثر مبيعاً</p>
                        </div>
                    <?php else: ?>
                        <?php foreach ($bestsellers as $index => $bestseller): ?>
                            <div class="bestseller-card rank-<?php echo $index + 1; ?>">
                                <div class="rank-badge"><?php echo $index + 1; ?></div>
                                <div class="product-image">
                                    <img src="<?php echo $bestseller['image'] ?: 'img/default-product.jpg'; ?>"
                                         alt="<?php echo htmlspecialchars($bestseller['description']); ?>">
                                </div>
                                <div class="product-details">
                                    <h3><?php echo htmlspecialchars($bestseller['description']); ?></h3>
                                </div>
                                <div class="bestseller-actions">
                                    <button class="btn btn-sm btn-primary">عرض التفاصيل</button>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </section>

            <!-- Categories Section -->
            <section id="categories-section" class="content-section <?php echo $active_section === 'categories' ? 'active' : ''; ?>">
                <div class="section-header">
                    <h2>إدارة الأقسام</h2>
                    <button class="btn btn-primary" onclick="openModal('category-modal')">
                        <i class="fas fa-plus"></i>
                        إضافة قسم جديد
                    </button>
                </div>

                <div class="categories-grid">
                    <?php if (empty($categories)): ?>
                        <div class="empty-state">
                            <i class="fas fa-tags"></i>
                            <p>لا توجد أقسام متاحة</p>
                        </div>
                    <?php else: ?>
                        <?php foreach ($categories as $category): ?>
                            <?php
                            // حساب عدد المنتجات في كل قسم
                            $product_count = 0;
                            foreach ($products as $product) {
                                if ($product['category'] === $category['name']) {
                                    $product_count++;
                                }
                            }
                            ?>
                            <div class="category-card">
                                <div class="category-image">
                                    <img src="<?php echo $category['image'] ?: 'img/default-category.jpg'; ?>"
                                         alt="<?php echo htmlspecialchars($category['name']); ?>">
                                    <div class="category-overlay">
                                        <button class="edit-category-btn"
                                                onclick="editCategory(<?php echo $category['id']; ?>)">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="category-info">
                                    <h3><?php echo htmlspecialchars($category['name']); ?></h3>
                                    <p class="product-count"><?php echo $product_count; ?> منتج</p>
                                    <p class="category-description">
                                        <?php echo htmlspecialchars($category['description'] ?: 'لا يوجد وصف'); ?>
                                    </p>
                                    <div class="category-status active">نشط</div>
                                </div>
                                <div class="category-actions">
                                    <button class="btn btn-sm btn-primary"
                                            onclick="editCategory(<?php echo $category['id']; ?>)">
                                        تعديل
                                    </button>
                                    <button class="btn btn-sm btn-danger"
                                            onclick="deleteCategory(<?php echo $category['id']; ?>)">
                                        حذف
                                    </button>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </section>
            <!-- End of Categories Section -->
        </div>
    </div>

    <!-- Product Modal -->
    <div id="product-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="product-modal-title">إضافة منتج جديد</h3>
                <span class="close-modal" onclick="closeModal('product-modal')">&times;</span>
            </div>
            <div class="modal-body">
                <form id="product-form" action="admin/process_actions.php" method="POST" enctype="multipart/form-data">
                    <input type="hidden" name="action" value="add_product">
                    <input type="hidden" name="product_id" id="product-id" value="">

                    <div class="form-group">
                        <label for="product-name">اسم المنتج *</label>
                        <input type="text" name="product_name" id="product-name" required>
                    </div>

                    <div class="form-group">
                        <label for="product-category">القسم *</label>
                        <select name="product_category" id="product-category" required>
                            <option value="">اختر القسم</option>
                            <?php foreach ($categories as $category): ?>
                                <option value="<?php echo htmlspecialchars($category['name']); ?>">
                                    <?php echo htmlspecialchars($category['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="product-price">السعر (ريال) *</label>
                            <input type="number" name="product_price" id="product-price" step="0.01" min="0" required>
                        </div>

                        <div class="form-group">
                            <label for="product-old-price">السعر القديم (ريال)</label>
                            <input type="number" name="product_old_price" id="product-old-price" step="0.01" min="0">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="product-description">الوصف</label>
                        <textarea name="product_description" id="product-description" rows="3"></textarea>
                    </div>

                    <div class="form-group">
                        <label for="product-image">صورة المنتج</label>
                        <input type="file" name="product_image" id="product-image" accept="image/*">
                        <small class="form-help">الحد الأقصى: 5MB. الأنواع المدعومة: JPG, PNG, GIF, WEBP</small>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            حفظ المنتج
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="closeModal('product-modal')">
                            <i class="fas fa-times"></i>
                            إلغاء
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Category Modal -->
    <div id="category-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="category-modal-title">إضافة قسم جديد</h3>
                <span class="close-modal" onclick="closeModal('category-modal')">&times;</span>
            </div>
            <div class="modal-body">
                <form id="category-form" action="admin/process_actions.php" method="POST" enctype="multipart/form-data">
                    <input type="hidden" name="action" value="add_category">
                    <input type="hidden" name="category_id" id="category-id" value="">

                    <div class="form-group">
                        <label for="category-name">اسم القسم *</label>
                        <input type="text" name="category_name" id="category-name" required>
                    </div>

                    <div class="form-group">
                        <label for="category-description">الوصف</label>
                        <textarea name="category_description" id="category-description" rows="3"></textarea>
                    </div>

                    <div class="form-group">
                        <label for="category-image">صورة القسم</label>
                        <input type="file" name="category_image" id="category-image" accept="image/*">
                        <small class="form-help">الحد الأقصى: 5MB. الأنواع المدعومة: JPG, PNG, GIF, WEBP</small>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            حفظ القسم
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="closeModal('category-modal')">
                            <i class="fas fa-times"></i>
                            إلغاء
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        // Modal Functions
        function openModal(modalId) {
            document.getElementById(modalId).style.display = 'block';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
            // Reset form
            const form = document.querySelector(`#${modalId} form`);
            if (form) {
                form.reset();
                // Reset hidden fields
                const actionInput = form.querySelector('input[name="action"]');
                const idInput = form.querySelector('input[name*="_id"]');
                if (actionInput && idInput) {
                    if (modalId === 'product-modal') {
                        actionInput.value = 'add_product';
                        document.getElementById('product-modal-title').textContent = 'إضافة منتج جديد';
                    } else if (modalId === 'category-modal') {
                        actionInput.value = 'add_category';
                        document.getElementById('category-modal-title').textContent = 'إضافة قسم جديد';
                    }
                    idInput.value = '';
                }
            }
        }

        // Edit Product
        function editProduct(productId) {
            // This would typically fetch product data via AJAX
            // For now, we'll just open the modal and set it to edit mode
            document.querySelector('input[name="action"]').value = 'edit_product';
            document.getElementById('product-id').value = productId;
            document.getElementById('product-modal-title').textContent = 'تعديل المنتج';
            openModal('product-modal');

            // In a real implementation, you would fetch the product data here
            // and populate the form fields
        }

        // Delete Product
        function deleteProduct(productId) {
            if (confirm('هل أنت متأكد من حذف هذا المنتج؟')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = 'admin/process_actions.php';

                const actionInput = document.createElement('input');
                actionInput.type = 'hidden';
                actionInput.name = 'action';
                actionInput.value = 'delete_product';

                const idInput = document.createElement('input');
                idInput.type = 'hidden';
                idInput.name = 'product_id';
                idInput.value = productId;

                form.appendChild(actionInput);
                form.appendChild(idInput);
                document.body.appendChild(form);
                form.submit();
            }
        }

        // Edit Category
        function editCategory(categoryId) {
            document.querySelector('#category-form input[name="action"]').value = 'edit_category';
            document.getElementById('category-id').value = categoryId;
            document.getElementById('category-modal-title').textContent = 'تعديل القسم';
            openModal('category-modal');
        }

        // Delete Category
        function deleteCategory(categoryId) {
            if (confirm('هل أنت متأكد من حذف هذا القسم؟ سيتم حذف جميع المنتجات المرتبطة به.')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = 'admin/process_actions.php';

                const actionInput = document.createElement('input');
                actionInput.type = 'hidden';
                actionInput.name = 'action';
                actionInput.value = 'delete_category';

                const idInput = document.createElement('input');
                idInput.type = 'hidden';
                idInput.name = 'category_id';
                idInput.value = categoryId;

                form.appendChild(actionInput);
                form.appendChild(idInput);
                document.body.appendChild(form);
                form.submit();
            }
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
        }

        // Auto-hide alerts after 5 seconds
        document.addEventListener('DOMContentLoaded', function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                setTimeout(() => {
                    alert.style.display = 'none';
                }, 5000);
            });
        });
    </script>
</body>
</html>
