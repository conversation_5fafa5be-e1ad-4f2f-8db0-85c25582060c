
// Sample Products Data
const productsData = [
    {
        id: 1,
        name: 'كريم أساس فائق التغطية',
        category: 'مكياج',
        price: 89.99,
        stock: 25,
        status: 'متوفر',
        image: 'img/foundation.jpg'
    },
    {
        id: 2,
        name: 'شامبو طبيعي للشعر الجاف',
        category: 'العناية بالشعر',
        price: 45.50,
        stock: 12,
        status: 'متوفر',
        image: 'img/shampoo.jpg'
    },
    {
        id: 3,
        name: 'مجموعة العناية الكاملة 1',
        category: 'منتجات عضوية',
        price: 199.99,
        stock: 8,
        status: 'متوفر',
        image: 'img/pack1.webp'
    },
    {
        id: 4,
        name: 'مقشر طبيعي للوجه',
        category: 'العناية بالبشرة',
        price: 35.75,
        stock: 0,
        status: 'نفد المخزون',
        image: 'img/scrub1.webp'
    },
    {
        id: 5,
        name: 'مجموعة العناية الكاملة 2',
        category: 'منتجات عضوية',
        price: 179.99,
        stock: 15,
        status: 'متوفر',
        image: 'img/pack2.webp'
    }
];

// DOM Elements
const sidebarNavItems = document.querySelectorAll('.nav-item a');
const contentSections = document.querySelectorAll('.content-section');
const pageTitle = document.getElementById('page-title');
const productsTableBody = document.getElementById('products-table-body');
const categoryFilter = document.getElementById('category-filter');
const statusFilter = document.getElementById('status-filter');
const productSearch = document.getElementById('product-search');
const sidebarToggle = document.querySelector('.sidebar-toggle');
const sidebar = document.querySelector('.sidebar');

// Initialize Dashboard
document.addEventListener('DOMContentLoaded', function() {
    console.log('Dashboard initializing...');

    // Check if required elements exist
    if (sidebarNavItems.length === 0) {
        console.error('No sidebar navigation items found');
    }

    if (contentSections.length === 0) {
        console.error('No content sections found');
    }

    // Initialize components
    loadProductsTable();
    setupEventListeners();
    setupResponsiveSidebar();

    // Set initial active section (check for saved state or default to dashboard)
    const savedSection = localStorage.getItem('activeSection') || 'dashboard';
    switchSection(savedSection);

    // Update active nav item based on saved section
    const activeNavItem = document.querySelector(`[data-section="${savedSection}"]`);
    if (activeNavItem) {
        sidebarNavItems.forEach(nav => nav.parentElement.classList.remove('active'));
        activeNavItem.parentElement.classList.add('active');
    }

    // Add keyboard navigation
    document.addEventListener('keydown', function(e) {
        // Alt + number keys for quick navigation
        if (e.altKey) {
            switch(e.key) {
                case '1':
                    e.preventDefault();
                    switchSection('dashboard');
                    updateActiveNavItem('dashboard');
                    break;
                case '2':
                    e.preventDefault();
                    switchSection('products');
                    updateActiveNavItem('products');
                    break;
                case '3':
                    e.preventDefault();
                    switchSection('bestsellers');
                    updateActiveNavItem('bestsellers');
                    break;
                case '4':
                    e.preventDefault();
                    switchSection('categories');
                    updateActiveNavItem('categories');
                    break;
            }
        }
    });

    console.log('Dashboard initialized successfully');
});

// Update Active Navigation Item
function updateActiveNavItem(section) {
    const activeNavItem = document.querySelector(`[data-section="${section}"]`);
    if (activeNavItem) {
        sidebarNavItems.forEach(nav => nav.parentElement.classList.remove('active'));
        activeNavItem.parentElement.classList.add('active');
        localStorage.setItem('activeSection', section);
    }
}

function setupEventListeners() {
    // Sidebar navigation
    sidebarNavItems.forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            const section = this.getAttribute('data-section');

            // Check if section exists
            if (section) {
                switchSection(section);

                // Save active section to localStorage
                localStorage.setItem('activeSection', section);

                // Update active nav item
                sidebarNavItems.forEach(nav => nav.parentElement.classList.remove('active'));
                this.parentElement.classList.add('active');

                // Close sidebar on mobile after navigation
                if (window.innerWidth <= 768) {
                    sidebar.classList.remove('active');
                }
            }
        });
    });
    
    // Product filters (only if elements exist)
    if (categoryFilter) categoryFilter.addEventListener('change', filterProducts);
    if (statusFilter) statusFilter.addEventListener('change', filterProducts);
    if (productSearch) productSearch.addEventListener('input', filterProducts);
    
    // Add product button
    const addProductBtn = document.getElementById('add-product-btn');
    if (addProductBtn) {
        addProductBtn.addEventListener('click', function() {
            alert('فتح نموذج إضافة منتج جديد');
        });
    }
    
    // Add category button
    const addCategoryBtn = document.getElementById('add-category-btn');
    if (addCategoryBtn) {
        addCategoryBtn.addEventListener('click', function() {
            alert('فتح نموذج إضافة قسم جديد');
        });
    }
    
    // Refresh bestsellers button
    const refreshBestsellersBtn = document.getElementById('refresh-bestsellers');
    if (refreshBestsellersBtn) {
        refreshBestsellersBtn.addEventListener('click', function() {
            alert('تم تحديث قائمة الأكثر مبيعاً');
        });
    }
}

// Setup Responsive Sidebar
function setupResponsiveSidebar() {
    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', function() {
            sidebar.classList.toggle('active');
        });
    }
    
    // Close sidebar when clicking outside on mobile
    document.addEventListener('click', function(e) {
        if (window.innerWidth <= 768) {
            if (!sidebar.contains(e.target) && !sidebarToggle.contains(e.target)) {
                sidebar.classList.remove('active');
            }
        }
    });
}

// Switch Section
function switchSection(sectionName) {
    console.log('Switching to section:', sectionName);

    // Hide all sections
    contentSections.forEach(section => {
        section.classList.remove('active');
    });

    // Show selected section
    const targetSection = document.getElementById(sectionName + '-section');
    if (targetSection) {
        targetSection.classList.add('active');
        console.log('Section activated:', sectionName + '-section');
    } else {
        console.error('Section not found:', sectionName + '-section');
        // Fallback to dashboard
        const dashboardSection = document.getElementById('dashboard-section');
        if (dashboardSection) {
            dashboardSection.classList.add('active');
        }
    }

    // Update page title
    const titles = {
        'dashboard': 'الرئيسية',
        'products': 'إدارة المنتجات',
        'bestsellers': 'الأكثر مبيعاً',
        'categories': 'إدارة الأقسام',
    };

    if (pageTitle) {
        pageTitle.textContent = titles[sectionName] || 'لوحة التحكم';
    }

    // Load specific section data if needed
    if (sectionName === 'products') {
        loadProductsTable();
        resetFilters();
    }
}

// Reset Filters
function resetFilters() {
    if (categoryFilter) categoryFilter.value = '';
    if (statusFilter) statusFilter.value = '';
    if (productSearch) productSearch.value = '';
}

// Load Products Table
function loadProductsTable(products = productsData) {
    if (!productsTableBody) return;

    productsTableBody.innerHTML = '';

    products.forEach(product => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>
                <img src="${product.image}" alt="${product.name}" class="product-image"
                     onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjZjBmMGYwIi8+CjxwYXRoIGQ9Ik0xMiAxNkwyOCAyNE0yOCAxNkwxMiAyNCIgc3Ryb2tlPSIjY2NjIiBzdHJva2Utd2lkdGg9IjIiLz4KPC9zdmc+'">
            </td>
            <td>${product.name}</td>
            <td>${product.category}</td>
            <td>${product.price.toFixed(2)} ريال</td>
            <td>${product.stock}</td>
            <td>
                <span class="status-badge ${product.status === 'متوفر' ? 'available' : 'out-of-stock'}">
                    ${product.status}
                </span>
            </td>
            <td>
                <div class="action-buttons">
                    <button class="btn btn-sm btn-primary" onclick="editProduct(${product.id})">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="deleteProduct(${product.id})">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        `;
        productsTableBody.appendChild(row);
    });
}

// Filter Products
function filterProducts() {
    const categoryValue = categoryFilter.value;
    const statusValue = statusFilter.value;
    const searchValue = productSearch.value.toLowerCase();
    
    let filteredProducts = productsData;
    
    // Filter by category
    if (categoryValue) {
        filteredProducts = filteredProducts.filter(product => product.category === categoryValue);
    }
    
    // Filter by status
    if (statusValue) {
        filteredProducts = filteredProducts.filter(product => product.status === statusValue);
    }
    
    // Filter by search
    if (searchValue) {
        filteredProducts = filteredProducts.filter(product => 
            product.name.toLowerCase().includes(searchValue)
        );
    }
    
    loadProductsTable(filteredProducts);
}

// Product Actions
function editProduct(productId) {
    const product = productsData.find(p => p.id === productId);
    if (product) {
        alert(`تعديل المنتج: ${product.name}`);
        // Here you would open an edit modal or navigate to edit page
    }
}

function deleteProduct(productId) {
    const product = productsData.find(p => p.id === productId);
    if (product) {
        if (confirm(`هل أنت متأكد من حذف المنتج: ${product.name}؟`)) {
            // Remove from data array
            const index = productsData.findIndex(p => p.id === productId);
            if (index > -1) {
                productsData.splice(index, 1);
                loadProductsTable();
                alert('تم حذف المنتج بنجاح');
            }
        }
    }
}


// Additional CSS for better styling
const additionalCSS = `
    .product-image {
        width: 50px;
        height: 50px;
        object-fit: cover;
        border-radius: 8px;
        border: 2px solid #f0f0f0;
    }

    .status-badge {
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 500;
    }

    .status-badge.available {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }

    .status-badge.out-of-stock {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }

    .action-buttons {
        display: flex;
        gap: 8px;
        justify-content: center;
    }

    .btn-sm {
        padding: 6px 12px;
        font-size: 0.875rem;
        border-radius: 6px;
        border: none;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .btn-primary {
        background: #d4a4a4;
        color: white;
    }

    .btn-primary:hover {
        background: #c19494;
        transform: translateY(-1px);
    }

    .btn-danger {
        background: #dc3545;
        color: white;
    }

    .btn-danger:hover {
        background: #c82333;
        transform: translateY(-1px);
    }

    .content-section {
        display: none;
        animation: fadeIn 0.3s ease-in-out;
    }

    .content-section.active {
        display: block;
    }

    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
`;

// Add the CSS to the document
const style = document.createElement('style');
style.textContent = additionalCSS;
document.head.appendChild(style);

// Simulate real-time updates
setInterval(function() {
    // Update notification count randomly
    const notificationCount = document.querySelector('.notification-count');
    if (notificationCount) {
        const randomCount = Math.floor(Math.random() * 10) + 1;
        notificationCount.textContent = randomCount;
    }
    
    // Update stats randomly (simulate real-time data)
    const statNumbers = document.querySelectorAll('.stat-number');
    statNumbers.forEach(stat => {
        if (stat.textContent.includes('ريال')) {
            const currentValue = parseInt(stat.textContent.replace(/[^\d]/g, ''));
            const change = Math.floor(Math.random() * 1000) - 500;
            const newValue = Math.max(0, currentValue + change);
            stat.textContent = newValue.toLocaleString() + ' ريال';
        }
    });
}, 30000); // Update every 30 seconds
